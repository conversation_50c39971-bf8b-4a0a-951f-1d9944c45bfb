# ModelSuite AI Agent Instructions

## Architecture Overview

ModelSuite is a comprehensive dual-role platform with **models** and **agencies** as separate user types, featuring:

- **Backend**: Express.js REST API with Socket.IO for real-time messaging
- **Frontend**: React 19 SPA with Vite, Tailwind CSS, and role-based routing
- **Database**: MongoDB with Mongoose ODM
- **Auth**: JWT tokens with role-based access control (`model`/`agency`)
- **Real-time**: Socket.IO for messaging, live status, and collaborative features
- **File Storage**: Cloudinary for media uploads and sto### Frontend Development Workflow

```bash
# Key development commands
npm run dev          # Start Vite dev server (port 4000)
npm run build        # Production build with Vite
npm run preview      # Preview production build
npm run lint         # ESLint checking with new config
```

**Modern Tech Stack**:

- **React 19**: Latest React with improved hooks and concurrent features
- **Vite 6.3.5**: Fast build tool and dev server
- **TypeScript Support**: Built-in support via Vite plugin
- **ESLint 9**: Latest ESLint with flat config (`eslint.config.js`)
- **PostCSS**: CSS processing with Tailwind integration

**Component Development Pattern**:

1. Create feature-specific folders in `components/`
2. Use inline component definitions for page-specific UI elements
3. Extract reusable components to `components/ui/` or `components/Agency/`
4. Follow Tailwind-first styling approach with utility classes
5. Implement responsive design with mobile-first methodology
6. Use Framer Motion for animations and transitions
7. Integrate React Big Calendar for scheduling features
8. Implement Recharts for data visualization and analytics Domain Concepts

9. **Dual User System**: Models and Agencies are distinct entities with separate authentication flows
10. **Real-time Messaging**: Socket.IO handles DM, group, channel, and topic communications
11. **Social Media Integration**: TikTok (via TikAPI), Instagram, Facebook, YouTube analytics
12. **Task Management**: Built-in task system with comments, attachments, and assignments
13. **Contract Management**: PandaDoc integration for model-agency contracts
14. **Questionnaire System**: Templates, assignments, and answers for model onboarding
15. **Viral Trends Tracking**: Automated trend fetching and analysis
16. **Content Upload System**: Model portfolio and content management
17. **Group Communication**: Topic-based discussions and group messaging
18. **Calendar Integration**: Google Calendar sync for events and scheduling
19. **Billing System**: Integrated billing and payment management
20. **Support System**: FAQ and email support features

## Key File Structure Patterns

### Backend (`modelsuite-backend/`)

```
controllers/          # Business logic grouped by feature
├── modelController.js         # Model user CRUD & auth
├── agencyController.js        # Agency user CRUD & auth
├── userProfileController.js   # Profile management
├── pandadoccontroller.js      # Contract management
├── socialMedia/              # Platform integrations
│   ├── facebookController.js     # Facebook API integration
│   ├── tiktokController.js       # TikTok API via TikAPI
│   ├── insightsController.js     # Social media analytics
│   └── handleInstagramCallback.js # Instagram OAuth handling
├── messanger/               # Real-time chat features
│   ├── chatController.js         # General chat management
│   ├── dmController.js           # Direct messages
│   ├── groupController.js        # Group messaging
│   └── channelController.js      # Channel communications
├── task/                    # Task management
│   ├── taskController.js         # Task CRUD operations
│   └── commentController.js      # Task comments
├── questionnaire/           # Template, assignment & answer management
│   ├── templateController.js     # Questionnaire templates
│   ├── assignmentController.js   # Template assignments
│   └── answerController.js       # Model answers
├── viraltrends/            # Trending content analysis
│   ├── trendcontroller.js        # Trend data management
│   └── viralcontroller.js        # Viral content tracking
├── google/                 # Google services integration
│   ├── googleController.js       # Google auth & API
│   └── eventController.js        # Calendar events
├── group/                  # Group management
│   ├── topicController.js        # Topic management
│   └── groupTopicMessageController.js # Group messaging
├── billing/                # Payment & billing
│   └── billingController.js      # Billing operations
└── contentupload/          # Content management
    └── model.js                  # Model content uploads

models/               # Mongoose schemas
├── model.js                  # Model user schema
├── agency.js                 # Agency user schema
├── UserProfile.js            # User profile data
├── Contract.js               # Contract documents
├── ModelsContract.js         # Model-specific contracts
├── AgencytoModelMessage.js   # Agency-model communications
├── otp.js                   # OTP verification
├── messanger/               # Chat-related schemas
│   ├── channelConversation.js   # Channel conversations
│   ├── channelMessages.js       # Channel messages
│   ├── dmConversations.js       # Direct message conversations
│   ├── dmMessages.js            # Direct messages
│   ├── groupConversations.js    # Group conversations
│   └── groupMessages.js         # Group messages
├── Social Media/            # Social platform schemas
│   └── InstagramAccount.js      # Instagram account data
├── questionnaire/           # Template, assignment & answer schemas
│   ├── Template.js              # Questionnaire templates
│   ├── TemplateAssignment.js    # Template assignments
│   └── ModelAnswer.js           # Model answers
├── task/                   # Task management schemas
│   ├── task.js                  # Task definition
│   └── comment.js               # Task comments
├── viraltrends/            # Trend tracking schemas
│   └── trendschema.js           # Trend data structure
├── Group/                  # Group communication schemas
│   ├── Group.js                 # Group definition
│   ├── Topic.js                 # Discussion topics
│   └── groupTopicMessageModel.js # Topic messages
├── contentupload/          # Content management schemas
│   └── modelcontent.js          # Model content uploads
└── billing/                # Billing schemas
    └── [billing models]         # Payment & subscription data

sockets/              # Socket.IO event handlers
├── index.js                  # Central socket registration
├── agencyToModelSocket.js    # Agency-model real-time communication
└── messanger/               # Chat socket handlers
    ├── chatSocket.js            # General chat sockets
    ├── dmSockets.js             # Direct message sockets
    ├── groupSockets.js          # Group chat sockets
    └── channelSocket.js         # Channel communication sockets

routes/               # Express route definitions
├── modelRoutes.js            # Model user routes
├── agencyRoutes.js           # Agency user routes
├── profileRoutes.js          # Profile management routes
├── uploadRoutes.js           # File upload routes
├── pandadocroutes.js         # Contract management routes
├── questionnaire/           # Questionnaire routes
│   ├── templateRoutes.js        # Template CRUD
│   ├── assignmentRoutes.js      # Assignment management
│   └── answerRoutes.js          # Answer submission
├── socialMedia/            # Social platform routes
│   ├── instagramRoutes.js       # Instagram integration
│   └── tiktokRoutes.js          # TikTok integration
├── messanger/              # Messaging routes
│   ├── chatRoute.js             # Chat management
│   ├── dmRoutes.js              # Direct messages
│   └── groupRoutes.js           # Group messaging
├── google/                 # Google services routes
│   ├── googleRoutes.js          # Google auth & API
│   └── eventRoutes.js           # Calendar events
├── task/                   # Task management routes
│   └── taskRoutes.js            # Task operations
├── viraltrends/            # Trend tracking routes
│   └── viralroute.js            # Viral content routes
├── contentupload/          # Content management routes
│   └── uploadroutes.js          # Content upload routes
└── billing/                # Billing routes
    └── billing.js               # Payment operations

jobs/                # Background jobs
└── fetchtrends.js           # Automated trend fetching

utils/               # Utility functions
├── defaultTemplate.js       # Default questionnaire template
├── seedDefaultTemplate.js   # Template seeding utility
├── googleCalendar.js        # Google Calendar integration
├── sendOtpToEmail.js        # Email OTP sending
├── sendOtpToPhone.js        # SMS OTP sending (disabled)
├── tikapiClient.js          # TikTok API client
└── validator.js             # Input validation utilities

middlewares/         # Express middlewares
├── authMiddleware.js        # JWT authentication
├── roleCheck.js             # Role-based access control
├── multer.js                # File upload handling
├── cloudinaryUpload.js      # Cloudinary integration
└── attachmentMulter.js      # Attachment handling

config/              # Configuration files
└── cloudinary.js            # Cloudinary setup
```

### Frontend (`modelsuite-frontend/`)

```
src/
├── pages/                # Route components
│   ├── Model/           # Model user pages
│   │   ├── Dashboard.jsx        # Model dashboard with tasks & content
│   │   ├── Login.jsx            # Model authentication
│   │   ├── Register.jsx         # Model registration
│   │   ├── ForgotPassword.jsx   # Password recovery
│   │   ├── Questionnaires.jsx   # Model questionnaire assignments
│   │   └── Setting.jsx          # Model settings & profile
│   ├── Agency/          # Agency user pages
│   │   ├── Dashboard.jsx        # Agency dashboard with analytics
│   │   ├── Login.jsx            # Agency authentication
│   │   ├── Register.jsx         # Agency registration
│   │   ├── ForgotPasswordAgency.jsx # Agency password recovery
│   │   ├── ModelView.jsx        # Individual model insights
│   │   ├── Questionnaires.jsx   # Template & assignment management
│   │   ├── Settings.jsx         # Agency settings
│   │   └── AgencyProfile.jsx    # Agency profile management
│   ├── Home/            # Landing page
│   │   └── Home.jsx             # Public landing page
│   ├── Register.jsx     # General registration page
│   └── Sidebar/         # Sidebar component
│       └── Sidebar.jsx          # Agency navigation sidebar
├── layouts/             # Layout wrappers
│   ├── ModelLayout.jsx  # Minimal layout for models (no sidebar)
│   └── AgencyLayout.jsx # Full layout with sidebar + navbar
├── components/          # Feature-specific components
│   ├── questionnaire/   # Questionnaire components
│   │   ├── agency/             # Agency questionnaire management
│   │   ├── model/              # Model questionnaire responses
│   │   └── shared/             # Shared questionnaire components
│   ├── socialMedia/     # Social platform integrations & insights
│   │   ├── Success.jsx          # Instagram OAuth success
│   │   └── TiktokSection.jsx    # TikTok integration
│   ├── task/           # Task management components
│   │   ├── CreateTask.jsx       # Task creation form
│   │   ├── TaskList.jsx         # Task listing
│   │   ├── TaskDetails.jsx      # Task detail view
│   │   ├── ModelTask.jsx        # Model task interface
│   │   └── AttachmentPreview.jsx # File attachment preview
│   ├── contentUpload/  # Content management
│   │   └── main.jsx             # Content upload interface
│   ├── Support/        # Support system
│   │   ├── FAQ.jsx              # FAQ component
│   │   ├── FAQCard.jsx          # Individual FAQ items
│   │   ├── email.jsx            # Email support form
│   │   └── faqs.json            # FAQ data
│   ├── Billing/        # Billing-related components
│   ├── Calender/       # Calendar integration components
│   │   └── Success.jsx          # Google Calendar OAuth success
│   ├── MyProfile/      # Profile management
│   ├── Agency/         # Agency-specific UI components
│   │   ├── AgencyInfoCard.jsx   # Agency information display
│   │   ├── AgencySpecialites.jsx  # Agency specialties section (new)
│   │   ├── TeamSection.jsx        # Agency team section (new)
│   │   ├── Alert.jsx            # Alert component
│   │   ├── Button.jsx           # Button component
│   │   ├── Card.jsx             # Card layout
│   │   ├── CardContent.jsx      # Card content
│   │   ├── CardHeader.jsx       # Card header
│   │   ├── CardTitle.jsx        # Card title
│   │   ├── Input.jsx            # Input field
│   │   ├── Label.jsx            # Form label
│   │   ├── Select.jsx           # Select dropdown
│   │   └── TextArea.jsx         # Text area input
│   ├── ui/             # Reusable UI components
│   │   └── [shared UI components] # Button, Avatar, Modal, etc.
│   ├── agencyMessanger.jsx  # Agency-specific messaging interface
│   ├── modelMessanger.jsx   # Model-specific messaging interface
│   ├── ChatWindow.jsx  # Universal chat component
│   ├── Navbar.jsx      # Top navigation bar
│   ├── AuthDropDown.jsx # Authentication dropdown
│   ├── AgencyMenu.jsx  # Agency menu component
│   └── ThemeToggle.jsx # Theme switching component
├── utils/               # Utilities and helpers
│   ├── ProtectedRoute.jsx    # Role-based route protection
│   ├── questionnaireApi.js   # Centralized API client with interceptors
│   ├── functions.jsx         # Utility functions (time formatting, typing indicators)
│   ├── socket.js            # Socket.IO client configuration
│   ├── socketListeners.jsx  # Socket event listeners
│   ├── retryMechanism.js    # API retry logic
│   └── __tests__/           # Test files
├── context/             # React context providers
├── data/               # Static data and constants
└── globalstate/         # Redux store
    ├── store.jsx        # Redux store configuration
    ├── channelSlice.jsx # Channel state management
    ├── dmSlice.jsx      # Direct message state
    └── groupSlice.jsx   # Group state management
```

## Development Workflows

### Starting Development

```bash
# Backend (port 3000)
cd modelsuite-backend
npm run dev

# Frontend (port 4000) - Vite dev server
cd modelsuite-frontend
npm run dev
```

### Environment Setup

**Frontend (.env):**

```bash
VITE_API_BASE_URL=http://localhost:3000/api/v1
VITE_SOCKET_URL=http://localhost:3000
VITE_META_APP_ID=your_facebook_app_id
VITE_TIKAPI_CLIENT_ID=your_tikapi_client_id
VITE_TIKAPI_REDIRECT_URI=http://localhost:4000/tiktok/success
```

**Backend (.env):**

```bash
# Server Configuration
PORT=3000
SERVER_HOSTING_BASEURL=http://localhost:3000
FRONTEND_HOSTING_BASEURL=http://localhost:4000

# Database
MONGO_URI=mongodb://localhost:27017/modelsuite

# Authentication
JWT_SECRET=your_jwt_secret
LOGIN_REFRESH_TOKEN_SECRET=your_refresh_secret
LOGIN_ACCESS_TOKEN_SECRET=your_access_secret
LOGIN_REFRESH_TOKEN_EXPIRY=7d
LOGIN_ACCESS_TOKEN_EXPIRY=15m

# File Storage
CLOUDINARY_API_KEY=your_cloudinary_key
CLOUDINARY_API_SECRET=your_cloudinary_secret
CLOUDINARY_CLOUD_NAME=your_cloud_name

# Social Media APIs
META_APP_ID=your_facebook_app_id
META_APP_SECRET=your_facebook_secret
PAGE_ID=your_facebook_page_id
ACCESS_TOKEN=your_facebook_access_token
INSTAGRAM_BUSINESS_ID=your_instagram_business_id
USER_ACCESS_TOKEN=your_user_access_token
LONG_LIVED_TOKEN=your_long_lived_token

# TikTok Integration
TIKAPI_KEY=your_tikapi_key
TIKAPI_CLIENT_ID=your_tikapi_client_id
TIKAPI_REDIRECT_URI=http://localhost:4000/tiktok/success

# Google Services
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_REDIRECT_URI=your_google_redirect_uri

# External APIs
SERP_API_KEY=your_serp_api_key
NEWS_API_KEY=your_news_api_key
YOUTUBE_API_KEY=your_youtube_api_key
RAPIDAPI_KEY=your_rapidapi_key
TWITTER_BEARER_TOKEN=your_twitter_token

# Contract Management
PANDADOC_API_KEY=your_pandadoc_key
PANDADOC_API_URL=https://api.pandadoc.com

# Communication
EMAIL_USER=your_smtp_email
EMAIL_PASS=your_smtp_password
TWILIO_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_PHONE=your_twilio_phone
```

### Authentication Flow

- **Registration**: OTP verification via email (phone disabled)
- **Login**: JWT tokens stored in localStorage as `auth` object: `{token, user}`
- **Role Checking**: Use `verifyRole('model'|'agency')` middleware (backend)
- **Frontend**: `ProtectedRoute` component redirects based on role/auth status
- **API Client**: Axios interceptors auto-inject JWT from localStorage (`utils/questionnaireApi.js`)

### Frontend Routing Architecture

```jsx
// App.jsx structure - Updated routing with all current routes
<Routes>
  {/* Public routes */}
  <Route path="/" element={<Home />} />
  <Route path="/register" element={<Register />} />
  <Route path="/model/login" element={<Login />} />
  <Route path="/model/register" element={<Register />} />
  <Route path="/model/forgot-password" element={<ForgotPassword />} />
  <Route path="/agency/login" element={<AgencyLogin />} />
  <Route path="/agency/register" element={<AgencyRegister />} />
  <Route path="/agency/forgot-password" element={<ForgotPasswordAgency />} />

  {/* OAuth Success Pages */}
  <Route path="/instagram/success" element={<InstagramSuccess />} />
  <Route path="/calendar/success" element={<CalendarSuccess />} />
  <Route path="/tiktok/success" element={<TiktokSuccess />} />

  {/* Support Pages */}
  <Route path="/support/faqs" element={<FAQ />} />
  <Route path="/support/email" element={<Email_support />} />

  {/* Protected model routes - minimal layout */}
  <Route element={<ProtectedRoute allowedRole="model" />}>
    <Route element={<ModelLayout />}>
      <Route path="/model/dashboard" element={<ModelDashboard />} />
      <Route path="/model/questionnaires" element={<ModelDashboard />} />
      <Route
        path="/model/questionnaire/:assignmentId"
        element={<QuestionnaireForm />}
      />
    </Route>
  </Route>

  {/* Protected agency routes - full layout with sidebar */}
  <Route element={<ProtectedRoute allowedRole="agency" />}>
    <Route element={<AgencyLayout />}>
      <Route path="/agency/dashboard" element={<AgencyDashboard />} />
      <Route path="/agency/questionnaires" element={<Questionnaires />} />
      <Route
        path="/agency/questionnaires/templates"
        element={<Questionnaires />}
      />
      <Route
        path="/agency/questionnaires/assignments"
        element={<Questionnaires />}
      />
      <Route
        path="/agency/questionnaires/responses"
        element={<Questionnaires />}
      />
      <Route
        path="/agency/questionnaires/analytics"
        element={<Questionnaires />}
      />
      <Route
        path="/agency/dashboard/profile/:agencyName"
        element={<AgencyProfile />}
      />
      <Route path="/agency/dashboard/settings" element={<Settings />} />
      <Route
        path="/agency/model-view/:id"
        element={<CreatorInsightsDashboard />}
      />
    </Route>
  </Route>
</Routes>
```

### UI Component Patterns

**Inline Component Definitions**: Many pages define reusable components inline (Avatar, Button, Modal) rather than importing from `ui/` directory.

```jsx
// Common pattern in dashboards
const Avatar = ({ src, alt, fallback, className = "" }) => (
  <div
    className={`relative inline-flex items-center justify-center overflow-hidden bg-gray-700 border-2 border-gray-800 shadow-sm rounded-full ${className}`}
  >
    {src ? (
      <img src={src} alt={alt} className="w-full h-full object-cover" />
    ) : (
      <span className="text-sm font-medium text-white">{fallback}</span>
    )}
  </div>
);
```

### Socket.IO Integration

- **Connection**: Auto-registers users with `socket.emit('register', {userId})` in layouts
- **User Tracking**: `connectedUsers` Map tracks online status
- **Event Handlers**: Modular pattern in `sockets/messanger/` directory
- **CORS**: Configured for localhost:4000 and production URLs
- **Frontend Client**: Configured in `utils/socket.js` with WebSocket transport

```jsx
// Layout pattern for socket registration
useEffect(() => {
  if (user?._id) {
    socket.emit("register", { userId: user._id });
  }
}, [user]);
```

### Styling & UI Framework

- **CSS Framework**: Tailwind CSS v3.4.17 with custom scrollbar plugin (`tailwind-scrollbar`)
- **Theme**: Dark theme with gray-950/900 gradients for agency layouts, customizable themes
- **Icons**: Lucide React icons (v0.522.0) throughout the application
- **Animations**: Framer Motion (v12.20.1) for enhanced interactions and page transitions
- **Charts**: Recharts (v2.15.4) for analytics and data visualization
- **Responsive**: Mobile-first approach with Tailwind breakpoints
- **UI Components**: Mix of inline definitions and reusable components in `ui/` directory
- **Calendar**: React Big Calendar integration for scheduling
- **Toast Notifications**: React Toastify for user feedback
- **PDF Generation**: jsPDF with AutoTable for report generation
- **Emoji Support**: Emoji Picker React for enhanced messaging

## Critical Patterns & Conventions

### Model vs Agency Distinction

```javascript
// Backend: Always check user role in controllers
if (req.user?.role !== 'model') {
  return res.status(403).json({ error: 'Forbidden: Access denied' })
}

// Frontend: Role-based route protection
<Route element={<ProtectedRoute allowedRole="model" />}>
```

### Frontend State Management

- **Auth State**: localStorage `auth` object accessed throughout app
- **Redux**: Enhanced setup with slices for channel, DM, and group management
  - `channelSlice.jsx`: Channel state management
  - `dmSlice.jsx`: Direct message state tracking
  - `groupSlice.jsx`: Group communication state
- **Local State**: Heavy use of useState for component-level state
- **Socket State**: Real-time updates via Socket.IO events with dedicated listeners
- **API State**: Centralized through `questionnaireApi.js` with retry mechanisms

### API Integration Patterns

```jsx
// Centralized API client with auth interceptors
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  headers: { "Content-Type": "application/json" },
});

// Auto-inject JWT token
apiClient.interceptors.request.use((config) => {
  const token = JSON.parse(localStorage.getItem("auth"))?.token;
  if (token) config.headers.Authorization = `Bearer ${token}`;
  return config;
});
```

### Messaging System Architecture

**Dual Messenger Components**:

- `agencyMessanger.jsx`: Groups, channels, DMs for agencies
- `modelMessanger.jsx`: Model-specific messaging interface
- `ChatWindow.jsx`: Universal chat component used by both

**Real-time Features**:

- Typing indicators with `useTypingIndicator` hook
- Message status tracking
- Online/offline user status

````

### Database Relationships

- Models have optional `agencyId` reference to Agency
- Both entities have `role` field (immutable, defaults to respective type)
- Social media accounts linked via embedded objects in user schemas

### API Route Structure

- Base: `/api/v1/`
- Pattern: `/api/v1/{resource}` (e.g., `/api/v1/model`, `/api/v1/agency`)
- Messenger routes: `/api/v1/messanger/{type}` (dm, group, channel, chat)
- Questionnaire routes: `/api/v1/questionnaire/{type}` (templates, assignments, answers)
- Social Media routes: `/api/v1/{platform}` (instagram, tiktok, google)
- Task routes: `/api/v1/tasks`
- Content routes: `/api/v1/contentupload`
- Billing routes: `/api/v1/billing`
- Profile routes: `/api/v1/profile`
- Viral trends routes: `/api/v1/Trending`
- PandaDoc routes: `/api/v1/panda`
- Event routes: `/api/v1/event`

### Social Media Integration

- **TikTok**: Uses TikAPI SDK (`tikapi` package) with accountKey storage and OAuth flow
- **Instagram**: Facebook Graph API integration with business insights
- **Facebook**: Meta Business API for page management and analytics
- **Google**: Calendar, authentication services, and YouTube API integration
- **Twitter**: Bearer token authentication for trend analysis
- All social connections store tokens/keys in user model schemas
- OAuth success pages handle token exchange and user feedback
- Real-time insights and analytics dashboards for connected accounts

### Error Handling

- Consistent JSON error responses: `{error: "message"}`
- OTP system with 10-minute expiry
- Mongoose validation errors propagated to client

## Environment Dependencies

### Required Environment Variables

- `MONGO_URI`: MongoDB connection string
- `JWT_SECRET`: Token signing secret with additional refresh/access token secrets
- `CLOUDINARY_*`: Image upload service configuration
- `TIKAPI_KEY`: TikTok API access with client ID and redirect URI
- Social media API credentials (Instagram, Google, Facebook, Twitter, YouTube)
- `PANDADOC_API_KEY`: Contract generation service
- `TWILIO_*`: SMS OTP service (currently disabled)
- `EMAIL_*`: SMTP configuration for email notifications
- API keys for external services (SERP, News, RapidAPI)

### External Services

- **Cloudinary**: File uploads (images, documents, videos)
- **TikAPI**: TikTok profile and analytics data with OAuth integration
- **PandaDoc**: Contract generation and management with API integration
- **Twilio**: SMS OTP (currently disabled, email OTP active)
- **Google APIs**: Calendar, authentication, YouTube analytics
- **Facebook/Meta APIs**: Instagram and Facebook business insights
- **Email Service**: SMTP for OTP and notifications
- **Trend APIs**: SERP API, News API, RapidAPI for viral content tracking
- **Twitter API**: Bearer token for social media trend analysis

### Background Jobs

- **Trend Fetching**: Automated job (`fetchtrends.js`) runs via node-cron
- **Template Seeding**: Auto-seeds default questionnaire templates on startup
- **Real-time Processing**: Socket.IO handles live updates and messaging

## Common Development Tasks

### Adding New User Role Features

**Backend:**
1. Update respective controller (`modelController.js` or `agencyController.js`)
2. Add protected routes with role verification
3. Update database models if schema changes needed

**Frontend:**
1. Create pages in appropriate directory (`pages/Model/` or `pages/Agency/`)
2. Add routes to `App.jsx` with proper `ProtectedRoute` wrapper
3. Update layout component navigation if needed
4. Create feature-specific components in `components/` directory

### Frontend Development Workflow

```bash
# Key development commands
npm run dev          # Start Vite dev server (port 4000)
npm run build        # Production build
npm run preview      # Preview production build
npm run lint         # ESLint checking
````

**Component Development Pattern**:

1. Create feature-specific folders in `components/`
2. Use inline component definitions for page-specific UI elements
3. Extract reusable components to `components/ui/`
4. Follow Tailwind-first styling approach

### Data Fetching Patterns

```jsx
// Standard API call pattern with error handling
const [data, setData] = useState([]);
const [loading, setLoading] = useState(true);

useEffect(() => {
  const fetchData = async () => {
    try {
      const response = await apiClient.get("/endpoint");
      setData(response.data);
    } catch (error) {
      console.error("Error:", error);
      // Handle error (toast notification, etc.)
    } finally {
      setLoading(false);
    }
  };
  fetchData();
}, []);
```

### Socket.IO Features

**Backend:**

1. Create handler in `sockets/messanger/`
2. Register in `sockets/index.js`
3. Emit events from controllers using `req.app.get('io')`

**Frontend:**

1. Import socket from `utils/socket.js`
2. Register event listeners in useEffect hooks
3. Handle real-time updates in component state
4. Use typing indicators and online status patterns

```jsx
// Frontend socket usage pattern
import socket from "../utils/socket";

useEffect(() => {
  socket.on("eventName", (data) => {
    // Handle real-time data
    setStateUpdater(data);
  });

  return () => socket.off("eventName");
}, []);
```

### Social Media Integration

**Backend:**

1. Add API credentials to environment
2. Create controller in `controllers/socialMedia/`
3. Update user model schema for token storage
4. Add OAuth callback routes

**Frontend:**

1. Create success pages for OAuth callbacks (`/instagram/success`, `/tiktok/success`)
2. Build integration components in `components/socialMedia/`
3. Add insights dashboards (Instagram, TikTok analytics)
4. Handle redirect flows and token exchange

**Key Integration Points**:

- Instagram: Graph API for insights and posting
- TikTok: TikAPI SDK for analytics and content management
- Google Calendar: Event scheduling and management
- Facebook: Meta Business API integration

### Questionnaire System Integration

1. **Templates**: Agencies create questionnaire templates in `controllers/questionnaire/templateController.js`
2. **Assignments**: Agencies assign templates to models via `assignmentController.js`
3. **Answers**: Models submit answers through `answerController.js`
4. **Auto-seeding**: Default template seeded on server startup via `utils/seedDefaultTemplate.js`
5. **Role-based access**: Templates (agency-only), assignments (agency creates, model views), answers (model submits, agency views)

```javascript
// Questionnaire workflow example
// 1. Agency creates template
POST /api/v1/questionnaire/templates { title, description, sections }

// 2. Agency assigns to model
POST /api/v1/questionnaire/assignments { templateId, modelId }

// 3. Model views assignments
GET /api/v1/questionnaire/assignments/my

// 4. Model submits answers
POST /api/v1/questionnaire/answers { templateId, answers }

// 5. Agency views answers
GET /api/v1/questionnaire/answers/:modelId/:templateId
```

### New Features & Systems Integration

#### Support System

- **FAQ System**: Structured FAQ with categories and search functionality
- **Email Support**: Direct email support form with SMTP integration
- **Help Documentation**: Comprehensive help system for users

#### Content Management

- **Model Content Upload**: Portfolio and content management for models
- **File Attachment System**: Multer and Cloudinary integration for file handling
- **Media Preview**: Enhanced attachment preview with multiple format support

#### Viral Trends & Analytics

- **Automated Trend Fetching**: Background job for trend monitoring
- **Social Media Analytics**: Comprehensive insights from multiple platforms
- **Trend Analysis**: AI-powered trend analysis and recommendations

#### Enhanced Messaging

- **Topic-based Discussions**: Group messaging with topic organization
- **Real-time Typing Indicators**: Live typing status with user feedback
- **Message Status Tracking**: Read receipts and delivery confirmations
- **Emoji Support**: Rich emoji picker integration

#### Billing & Payments

- **Subscription Management**: Integrated billing system
- **Payment Processing**: Secure payment handling
- **Invoice Generation**: PDF invoice generation with jsPDF

#### Advanced Task Management

- **Task Comments**: Threaded comments on tasks
- **File Attachments**: Attach files to tasks and comments
- **Task Assignment**: Assign tasks to specific models
- **Progress Tracking**: Visual progress indicators and status updates

#### Calendar Integration

- **Google Calendar Sync**: Bidirectional calendar synchronization
- **Event Management**: Create and manage events within the platform
- **Scheduling**: Integrated scheduling system for model-agency meetings

Remember: This platform serves two distinct user bases with different needs, so always consider the role-based context when implementing features. The system has evolved significantly with enhanced real-time features, comprehensive social media integration, and advanced content management capabilities.
