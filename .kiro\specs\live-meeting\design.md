# Design Document

## Overview

The live meeting feature integrates Jitsi Meet for video conferencing, server-side transcription, Google Calendar sync, and secure recording storage into the ModelSuite platform. The system follows a microservices-inspired architecture with clear separation between meeting management, video infrastructure, transcription services, and storage systems.

## Architecture

### Production-Grade System Architecture

```mermaid
graph TB
    subgraph "Load Balancer & CDN"
        LB[NGINX Load Balancer]
        CDN[CloudFront CDN]
    end
    
    subgraph "Frontend Layer - React/Next.js"
        A[Agency Dashboard] --> B[Meeting Interface]
        C[Model Dashboard] --> B
        B --> D[Jitsi Meet Component]
        B --> E[Meeting Lobby]
        B --> F[Post-Meeting UI]
        G[Error Boundary] --> B
        H[State Management - Redux]
    end
    
    subgraph "API Gateway & Authentication"
        AG[API Gateway]
        AUTH[JWT Auth Middleware]
        RATE[Rate Limiter]
        VALID[Request Validator]
    end
    
    subgraph "Microservices Architecture"
        subgraph "Meeting Service"
            MS[Meeting Controller]
            MSL[Meeting Service Layer]
            MSR[Meeting Repository]
        end
        
        subgraph "Calendar Service"
            CS[Calendar Controller]
            CSL[Calendar Service Layer]
            CSR[Calendar Repository]
        end
        
        subgraph "Recording Service"
            RS[Recording Controller]
            RSL[Recording Service Layer]
            RSR[Recording Repository]
        end
        
        subgraph "Transcription Service"
            TS[Transcription Controller]
            TSL[Transcription Service Layer]
            TSR[Transcription Repository]
        end
        
        subgraph "Notification Service"
            NS[Notification Controller]
            NSL[Notification Service Layer]
            NSR[Notification Repository]
        end
    end
    
    subgraph "Message Queue & Event Bus"
        MQ[Redis Message Queue]
        EB[Event Bus - Socket.IO]
        SCHED[Task Scheduler - Bull]
    end
    
    subgraph "External Services"
        JITSI[Self-Hosted Jitsi Meet]
        GCAL[Google Calendar API]
        S3[AWS S3 + CloudFront]
        TRANS[AWS Transcribe/Azure Speech]
        EMAIL[SendGrid/SES]
    end
    
    subgraph "Data Layer"
        subgraph "Primary Database"
            MONGO[MongoDB Replica Set]
            MONGOIDX[Optimized Indexes]
        end
        
        subgraph "Caching Layer"
            REDIS[Redis Cluster]
            REDISSES[Redis Sessions]
        end
        
        subgraph "Search & Analytics"
            ELASTIC[Elasticsearch]
            KIBANA[Kibana Dashboard]
        end
    end
    
    subgraph "Monitoring & Observability"
        PROM[Prometheus]
        GRAF[Grafana]
        JAEGER[Jaeger Tracing]
        SENTRY[Sentry Error Tracking]
    end
    
    subgraph "Security Layer"
        WAF[Web Application Firewall]
        VAULT[HashiCorp Vault]
        AUDIT[Audit Logging]
    end
    
    LB --> AG
    CDN --> A
    AG --> AUTH
    AUTH --> RATE
    RATE --> VALID
    VALID --> MS
    VALID --> CS
    VALID --> RS
    VALID --> TS
    VALID --> NS
    
    MSL --> MQ
    CSL --> MQ
    RSL --> MQ
    TSL --> MQ
    NSL --> MQ
    
    MSR --> MONGO
    CSR --> MONGO
    RSR --> MONGO
    TSR --> MONGO
    NSR --> MONGO
    
    MSL --> REDIS
    CSL --> REDIS
    RSL --> REDIS
    TSL --> REDIS
    NSL --> REDIS
    
    CS --> GCAL
    RS --> S3
    TS --> TRANS
    NS --> EMAIL
    B --> JITSI
    
    MS --> EB
    CS --> EB
    RS --> EB
    TS --> EB
    NS --> EB
```

### Architectural Principles

1. **Microservices Architecture**: Each service is independently deployable and scalable
2. **Event-Driven Design**: Services communicate via events for loose coupling
3. **CQRS Pattern**: Separate read and write operations for optimal performance
4. **Circuit Breaker Pattern**: Fault tolerance for external service calls
5. **Repository Pattern**: Clean separation between business logic and data access
6. **Dependency Injection**: Testable and maintainable code structure

### System Components

1. **Meeting Management Service**: Core business logic for meeting lifecycle
2. **Video Infrastructure**: Jitsi Meet integration with custom authentication
3. **Calendar Integration**: Google Calendar API for scheduling and notifications
4. **Recording System**: Encrypted storage and retrieval of meeting recordings
5. **Transcription Service**: Real-time speech-to-text processing
6. **Notification System**: Real-time updates via Socket.IO and email/calendar notifications

## Components and Interfaces

### Production-Grade Backend Architecture

#### Domain Models with Validation

```javascript
// Meeting Domain Model with comprehensive validation
const meetingSchema = new mongoose.Schema({
  _id: {
    type: mongoose.Schema.Types.ObjectId,
    default: () => new mongoose.Types.ObjectId()
  },
  title: {
    type: String,
    required: [true, 'Meeting title is required'],
    trim: true,
    minlength: [3, 'Title must be at least 3 characters'],
    maxlength: [100, 'Title cannot exceed 100 characters'],
    validate: {
      validator: (v) => /^[a-zA-Z0-9\s\-_.,!?]+$/.test(v),
      message: 'Title contains invalid characters'
    }
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  agencyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Agency',
    required: [true, 'Agency ID is required'],
    index: true
  },
  participants: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'ModelUser',
      required: true
    },
    role: {
      type: String,
      enum: {
        values: ['organizer', 'participant'],
        message: 'Role must be either organizer or participant'
      },
      required: true
    },
    status: {
      type: String,
      enum: {
        values: ['invited', 'accepted', 'rejected', 'attended', 'no-show'],
        message: 'Invalid participant status'
      },
      default: 'invited'
    },
    invitedAt: {
      type: Date,
      default: Date.now
    },
    respondedAt: Date,
    joinedAt: Date,
    leftAt: Date,
    connectionQuality: {
      type: String,
      enum: ['excellent', 'good', 'fair', 'poor'],
      default: 'good'
    }
  }],
  type: {
    type: String,
    enum: {
      values: ['instant', 'scheduled'],
      message: 'Meeting type must be instant or scheduled'
    },
    required: true
  },
  status: {
    type: String,
    enum: {
      values: ['scheduled', 'in-progress', 'completed', 'cancelled', 'failed'],
      message: 'Invalid meeting status'
    },
    default: 'scheduled',
    index: true
  },
  scheduledAt: {
    type: Date,
    validate: {
      validator: function(v) {
        return this.type === 'instant' || (v && v > new Date());
      },
      message: 'Scheduled meetings must have a future date'
    }
  },
  startedAt: Date,
  endedAt: Date,
  actualDuration: Number, // in seconds
  maxDuration: {
    type: Number,
    default: 900, // 15 minutes in seconds
    min: [300, 'Minimum meeting duration is 5 minutes'],
    max: [3600, 'Maximum meeting duration is 60 minutes']
  },
  jitsiRoomId: {
    type: String,
    unique: true,
    sparse: true
  },
  jitsiJWT: {
    type: String,
    select: false // Never return in queries
  },
  jwtExpiresAt: Date,
  calendarEventId: String,
  recording: {
    enabled: {
      type: Boolean,
      default: false
    },
    s3Key: String,
    s3Bucket: String,
    encryptionKey: {
      type: String,
      select: false
    },
    fileSize: Number,
    duration: Number,
    downloadCount: {
      type: Number,
      default: 0
    },
    lastDownloadedAt: Date,
    downloadedBy: [{
      userId: mongoose.Schema.Types.ObjectId,
      downloadedAt: Date,
      ipAddress: String
    }]
  },
  transcript: {
    content: String,
    speakers: [{
      name: String,
      userId: mongoose.Schema.Types.ObjectId,
      segments: [{
        text: String,
        timestamp: Date,
        confidence: {
          type: Number,
          min: 0,
          max: 1
        },
        duration: Number
      }]
    }],
    language: {
      type: String,
      default: 'en-US'
    },
    processingStatus: {
      type: String,
      enum: ['pending', 'processing', 'completed', 'failed'],
      default: 'pending'
    },
    processingStartedAt: Date,
    processingCompletedAt: Date,
    wordCount: Number,
    confidence: Number
  },
  feedback: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true
    },
    rating: {
      type: Number,
      min: [1, 'Rating must be at least 1'],
      max: [5, 'Rating cannot exceed 5'],
      required: true
    },
    comments: {
      type: String,
      maxlength: [1000, 'Comments cannot exceed 1000 characters']
    },
    categories: [{
      type: String,
      enum: ['audio_quality', 'video_quality', 'connection', 'features', 'overall']
    }],
    submittedAt: {
      type: Date,
      default: Date.now
    }
  }],
  metadata: {
    maxParticipants: {
      type: Number,
      default: 5,
      min: [2, 'Minimum 2 participants required'],
      max: [10, 'Maximum 10 participants allowed']
    },
    recordingPaid: {
      type: Boolean,
      default: false
    },
    priority: {
      type: String,
      enum: ['low', 'normal', 'high', 'urgent'],
      default: 'normal'
    },
    tags: [String],
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      required: true
    },
    lastModifiedBy: mongoose.Schema.Types.ObjectId,
    version: {
      type: Number,
      default: 1
    }
  },
  auditLog: [{
    action: String,
    userId: mongoose.Schema.Types.ObjectId,
    timestamp: {
      type: Date,
      default: Date.now
    },
    details: mongoose.Schema.Types.Mixed
  }]
}, {
  timestamps: true,
  versionKey: false,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for optimal query performance
meetingSchema.index({ agencyId: 1, status: 1, scheduledAt: -1 });
meetingSchema.index({ 'participants.userId': 1, status: 1 });
meetingSchema.index({ createdAt: -1 });
meetingSchema.index({ jitsiRoomId: 1 }, { unique: true, sparse: true });
meetingSchema.index({ 'transcript.content': 'text' }); // Full-text search

// Virtual fields
meetingSchema.virtual('isActive').get(function() {
  return this.status === 'in-progress';
});

meetingSchema.virtual('canRecord').get(function() {
  return this.metadata.recordingPaid && this.recording.enabled;
});

// Pre-save middleware for validation and business logic
meetingSchema.pre('save', async function(next) {
  if (this.isNew) {
    this.jitsiRoomId = generateSecureRoomId();
  }
  
  if (this.isModified('status') && this.status === 'in-progress') {
    this.startedAt = new Date();
  }
  
  if (this.isModified('status') && this.status === 'completed') {
    this.endedAt = new Date();
    if (this.startedAt) {
      this.actualDuration = Math.floor((this.endedAt - this.startedAt) / 1000);
    }
  }
  
  next();
});

// Static methods for business operations
meetingSchema.statics.findActiveByUser = function(userId) {
  return this.find({
    $or: [
      { agencyId: userId },
      { 'participants.userId': userId }
    ],
    status: 'in-progress'
  });
};

meetingSchema.statics.findUpcomingByUser = function(userId, limit = 10) {
  return this.find({
    $or: [
      { agencyId: userId },
      { 'participants.userId': userId }
    ],
    status: 'scheduled',
    scheduledAt: { $gte: new Date() }
  })
  .sort({ scheduledAt: 1 })
  .limit(limit);
};
```

#### Enterprise-Grade Service Layer

```javascript
// Meeting Controller with comprehensive error handling and validation
class MeetingController {
  constructor(meetingService, validationService, auditService) {
    this.meetingService = meetingService;
    this.validationService = validationService;
    this.auditService = auditService;
  }

  // Meeting CRUD operations with comprehensive validation
  async createMeeting(req, res) {
    try {
      const validationResult = await this.validationService.validateCreateMeeting(req.body);
      if (!validationResult.isValid) {
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          details: validationResult.errors,
          timestamp: new Date().toISOString()
        });
      }

      const meeting = await this.meetingService.createMeeting({
        ...req.body,
        createdBy: req.user.id,
        agencyId: req.user.agencyId
      });

      await this.auditService.logAction({
        action: 'MEETING_CREATED',
        userId: req.user.id,
        resourceId: meeting._id,
        details: { title: meeting.title, type: meeting.type }
      });

      res.status(201).json({
        success: true,
        data: meeting,
        message: 'Meeting created successfully'
      });
    } catch (error) {
      await this.handleError(error, req, res, 'CREATE_MEETING');
    }
  }

  async getMeetings(req, res) {
    try {
      const { page = 1, limit = 10, status, type, search } = req.query;
      const filters = this.buildMeetingFilters(req.user, { status, type, search });
      
      const result = await this.meetingService.getMeetingsPaginated(filters, {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), 50), // Max 50 per page
        sort: { scheduledAt: -1, createdAt: -1 }
      });

      res.json({
        success: true,
        data: result.meetings,
        pagination: {
          page: result.page,
          limit: result.limit,
          total: result.total,
          pages: result.pages
        }
      });
    } catch (error) {
      await this.handleError(error, req, res, 'GET_MEETINGS');
    }
  }

  async getMeetingById(req, res) {
    try {
      const { id } = req.params;
      const meeting = await this.meetingService.getMeetingById(id);
      
      if (!meeting) {
        return res.status(404).json({
          success: false,
          error: 'MEETING_NOT_FOUND',
          message: 'Meeting not found'
        });
      }

      // Check access permissions
      const hasAccess = await this.meetingService.validateUserAccess(meeting, req.user.id);
      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          error: 'ACCESS_DENIED',
          message: 'You do not have permission to access this meeting'
        });
      }

      res.json({
        success: true,
        data: meeting
      });
    } catch (error) {
      await this.handleError(error, req, res, 'GET_MEETING');
    }
  }

  // Meeting lifecycle with state management
  async startMeeting(req, res) {
    try {
      const { id } = req.params;
      const meeting = await this.meetingService.startMeeting(id, req.user.id);
      
      // Generate Jitsi JWT token
      const jwtToken = await this.meetingService.generateJitsiJWT(
        meeting.jitsiRoomId,
        req.user.id,
        meeting.agencyId.toString() === req.user.id ? 'moderator' : 'participant'
      );

      // Start recording if enabled
      if (meeting.canRecord) {
        await this.meetingService.startRecording(id);
      }

      // Start transcription if enabled
      if (meeting.metadata.transcriptionEnabled) {
        await this.meetingService.startTranscription(id);
      }

      res.json({
        success: true,
        data: {
          meeting,
          jwtToken,
          jitsiConfig: {
            roomName: meeting.jitsiRoomId,
            jwt: jwtToken,
            domain: process.env.JITSI_DOMAIN,
            configOverwrite: {
              startWithAudioMuted: true,
              startWithVideoMuted: false,
              enableWelcomePage: false,
              prejoinPageEnabled: true
            }
          }
        }
      });
    } catch (error) {
      await this.handleError(error, req, res, 'START_MEETING');
    }
  }

  async joinMeeting(req, res) {
    try {
      const { id } = req.params;
      const result = await this.meetingService.joinMeeting(id, req.user.id);
      
      res.json({
        success: true,
        data: result,
        message: 'Successfully joined meeting'
      });
    } catch (error) {
      await this.handleError(error, req, res, 'JOIN_MEETING');
    }
  }

  async endMeeting(req, res) {
    try {
      const { id } = req.params;
      const meeting = await this.meetingService.endMeeting(id, req.user.id);
      
      res.json({
        success: true,
        data: meeting,
        message: 'Meeting ended successfully'
      });
    } catch (error) {
      await this.handleError(error, req, res, 'END_MEETING');
    }
  }

  // Error handling with proper logging and monitoring
  async handleError(error, req, res, operation) {
    const errorId = generateErrorId();
    
    // Log error with context
    logger.error(`${operation} failed`, {
      errorId,
      error: error.message,
      stack: error.stack,
      userId: req.user?.id,
      path: req.path,
      method: req.method,
      body: req.body,
      params: req.params,
      query: req.query
    });

    // Send to monitoring service
    await this.monitoringService.recordError(error, {
      operation,
      userId: req.user?.id,
      errorId
    });

    // Return appropriate error response
    const statusCode = error.statusCode || 500;
    const errorResponse = {
      success: false,
      error: error.code || 'INTERNAL_ERROR',
      message: statusCode === 500 ? 'An internal error occurred' : error.message,
      errorId,
      timestamp: new Date().toISOString()
    };

    if (process.env.NODE_ENV === 'development') {
      errorResponse.stack = error.stack;
    }

    res.status(statusCode).json(errorResponse);
  }

  // Helper methods
  buildMeetingFilters(user, queryFilters) {
    const baseFilter = {
      $or: [
        { agencyId: user.id },
        { 'participants.userId': user.id }
      ]
    };

    if (queryFilters.status) {
      baseFilter.status = queryFilters.status;
    }

    if (queryFilters.type) {
      baseFilter.type = queryFilters.type;
    }

    if (queryFilters.search) {
      baseFilter.$text = { $search: queryFilters.search };
    }

    return baseFilter;
  }
}
```

#### Production-Grade Meeting Service

```javascript
class MeetingService {
  constructor(
    meetingRepository,
    calendarService,
    notificationService,
    recordingService,
    transcriptionService,
    jitsiService,
    cacheService,
    eventBus,
    logger
  ) {
    this.meetingRepository = meetingRepository;
    this.calendarService = calendarService;
    this.notificationService = notificationService;
    this.recordingService = recordingService;
    this.transcriptionService = transcriptionService;
    this.jitsiService = jitsiService;
    this.cacheService = cacheService;
    this.eventBus = eventBus;
    this.logger = logger;
  }

  async createMeeting(meetingData) {
    const transaction = await this.meetingRepository.startTransaction();
    
    try {
      // Validate business rules
      await this.validateMeetingCreation(meetingData);
      
      // Create meeting with optimistic locking
      const meeting = await this.meetingRepository.create({
        ...meetingData,
        jitsiRoomId: this.generateSecureRoomId(),
        status: meetingData.type === 'instant' ? 'in-progress' : 'scheduled'
      }, { transaction });

      // Create calendar event for scheduled meetings
      if (meeting.type === 'scheduled') {
        const calendarEvent = await this.calendarService.createEvent({
          title: meeting.title,
          description: meeting.description,
          startTime: meeting.scheduledAt,
          duration: meeting.maxDuration,
          participants: meeting.participants.map(p => p.userId)
        });
        
        meeting.calendarEventId = calendarEvent.id;
        await meeting.save({ transaction });
      }

      // Send invitations
      await this.sendMeetingInvitations(meeting);

      // Cache meeting data
      await this.cacheService.set(
        `meeting:${meeting._id}`,
        meeting,
        { ttl: 3600 } // 1 hour
      );

      // Emit event
      this.eventBus.emit('meeting.created', {
        meetingId: meeting._id,
        agencyId: meeting.agencyId,
        participants: meeting.participants,
        type: meeting.type
      });

      await transaction.commit();
      
      this.logger.info('Meeting created successfully', {
        meetingId: meeting._id,
        agencyId: meeting.agencyId,
        type: meeting.type
      });

      return meeting;
    } catch (error) {
      await transaction.rollback();
      this.logger.error('Failed to create meeting', {
        error: error.message,
        meetingData
      });
      throw error;
    }
  }

  async startMeeting(meetingId, userId) {
    const meeting = await this.getMeetingById(meetingId);
    
    if (!meeting) {
      throw new NotFoundError('Meeting not found');
    }

    if (!await this.validateUserAccess(meeting, userId)) {
      throw new ForbiddenError('Access denied');
    }

    if (meeting.status !== 'scheduled') {
      throw new BadRequestError('Meeting cannot be started');
    }

    // Update meeting status with optimistic locking
    const updatedMeeting = await this.meetingRepository.updateWithVersion(
      meetingId,
      {
        status: 'in-progress',
        startedAt: new Date(),
        'metadata.version': meeting.metadata.version + 1
      },
      meeting.metadata.version
    );

    // Generate JWT token for Jitsi
    const jwtToken = await this.generateJitsiJWT(
      meeting.jitsiRoomId,
      userId,
      meeting.agencyId.toString() === userId ? 'moderator' : 'participant'
    );

    // Start recording if enabled
    if (meeting.canRecord) {
      await this.recordingService.startRecording(meetingId);
    }

    // Start transcription if enabled
    if (meeting.metadata.transcriptionEnabled) {
      await this.transcriptionService.startTranscription(meetingId);
    }

    // Schedule automatic meeting end
    await this.scheduleAutomaticEnd(meetingId, meeting.maxDuration);

    // Update cache
    await this.cacheService.set(`meeting:${meetingId}`, updatedMeeting);

    // Emit real-time event
    this.eventBus.emit('meeting.started', {
      meetingId,
      agencyId: meeting.agencyId,
      participants: meeting.participants
    });

    return { meeting: updatedMeeting, jwtToken };
  }

  async joinMeeting(meetingId, userId) {
    const meeting = await this.getMeetingById(meetingId);
    
    if (!meeting || !await this.validateUserAccess(meeting, userId)) {
      throw new ForbiddenError('Access denied');
    }

    if (meeting.status !== 'in-progress') {
      throw new BadRequestError('Meeting is not active');
    }

    // Update participant status
    const participant = meeting.participants.find(p => 
      p.userId.toString() === userId
    );
    
    if (participant) {
      participant.status = 'attended';
      participant.joinedAt = new Date();
      await meeting.save();
    }

    // Generate JWT token
    const jwtToken = await this.generateJitsiJWT(
      meeting.jitsiRoomId,
      userId,
      meeting.agencyId.toString() === userId ? 'moderator' : 'participant'
    );

    // Emit real-time event
    this.eventBus.emit('meeting.participant_joined', {
      meetingId,
      userId,
      participantCount: meeting.participants.filter(p => p.status === 'attended').length
    });

    return { meeting, jwtToken };
  }

  async endMeeting(meetingId, userId) {
    const meeting = await this.getMeetingById(meetingId);
    
    if (!meeting) {
      throw new NotFoundError('Meeting not found');
    }

    // Only organizer can end meeting
    if (meeting.agencyId.toString() !== userId) {
      throw new ForbiddenError('Only organizer can end meeting');
    }

    if (meeting.status !== 'in-progress') {
      throw new BadRequestError('Meeting is not active');
    }

    // Update meeting status
    meeting.status = 'completed';
    meeting.endedAt = new Date();
    meeting.actualDuration = Math.floor(
      (meeting.endedAt - meeting.startedAt) / 1000
    );
    await meeting.save();

    // Stop recording
    if (meeting.recording.enabled) {
      await this.recordingService.stopRecording(meetingId);
    }

    // Finalize transcription
    if (meeting.transcript.processingStatus === 'processing') {
      await this.transcriptionService.finalizeTranscript(meetingId);
    }

    // Clean up Jitsi room
    await this.jitsiService.destroyRoom(meeting.jitsiRoomId);

    // Update cache
    await this.cacheService.set(`meeting:${meetingId}`, meeting);

    // Emit event
    this.eventBus.emit('meeting.ended', {
      meetingId,
      agencyId: meeting.agencyId,
      duration: meeting.actualDuration,
      participants: meeting.participants
    });

    // Schedule post-meeting tasks
    await this.schedulePostMeetingTasks(meetingId);

    return meeting;
  }

  async generateJitsiJWT(roomId, userId, role) {
    const payload = {
      iss: process.env.JITSI_APP_ID,
      sub: process.env.JITSI_DOMAIN,
      aud: 'jitsi',
      exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
      room: roomId,
      context: {
        user: {
          id: userId,
          name: await this.getUserName(userId),
          avatar: await this.getUserAvatar(userId)
        },
        features: {
          livestreaming: role === 'moderator',
          recording: role === 'moderator',
          transcription: true
        }
      },
      moderator: role === 'moderator'
    };

    return jwt.sign(payload, process.env.JITSI_SECRET, { algorithm: 'HS256' });
  }

  async validateUserAccess(meeting, userId) {
    // Check if user is organizer
    if (meeting.agencyId.toString() === userId) {
      return true;
    }

    // Check if user is participant
    return meeting.participants.some(p => 
      p.userId.toString() === userId && 
      ['accepted', 'attended'].includes(p.status)
    );
  }

  async getMeetingsPaginated(filters, options) {
    const cacheKey = `meetings:${JSON.stringify(filters)}:${JSON.stringify(options)}`;
    
    // Try cache first
    const cached = await this.cacheService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const result = await this.meetingRepository.findPaginated(filters, options);
    
    // Cache for 5 minutes
    await this.cacheService.set(cacheKey, result, { ttl: 300 });
    
    return result;
  }

  async getMeetingById(meetingId) {
    // Try cache first
    const cached = await this.cacheService.get(`meeting:${meetingId}`);
    if (cached) {
      return cached;
    }

    const meeting = await this.meetingRepository.findById(meetingId);
    if (meeting) {
      await this.cacheService.set(`meeting:${meetingId}`, meeting, { ttl: 3600 });
    }

    return meeting;
  }

  // Private helper methods
  generateSecureRoomId() {
    return `ms_${crypto.randomBytes(16).toString('hex')}_${Date.now()}`;
  }

  async validateMeetingCreation(meetingData) {
    // Check participant limits
    if (meetingData.participants.length > 10) {
      throw new BadRequestError('Maximum 10 participants allowed');
    }

    // Check scheduling conflicts for scheduled meetings
    if (meetingData.type === 'scheduled') {
      const conflicts = await this.findSchedulingConflicts(
        meetingData.agencyId,
        meetingData.scheduledAt,
        meetingData.maxDuration
      );
      
      if (conflicts.length > 0) {
        throw new ConflictError('Scheduling conflict detected');
      }
    }

    // Validate participant availability
    await this.validateParticipantAvailability(
      meetingData.participants,
      meetingData.scheduledAt
    );
  }

  async scheduleAutomaticEnd(meetingId, duration) {
    const job = await this.taskScheduler.schedule(
      'endMeeting',
      { meetingId },
      { delay: duration * 1000 }
    );

    // Store job ID for potential cancellation
    await this.cacheService.set(
      `meeting:${meetingId}:autoEndJob`,
      job.id,
      { ttl: duration + 60 }
    );
  }

  async schedulePostMeetingTasks(meetingId) {
    // Schedule feedback collection
    await this.taskScheduler.schedule(
      'sendFeedbackRequest',
      { meetingId },
      { delay: 300000 } // 5 minutes
    );

    // Schedule meeting summary generation
    await this.taskScheduler.schedule(
      'generateMeetingSummary',
      { meetingId },
      { delay: 600000 } // 10 minutes
    );
  }
}
```

#### Production-Grade Calendar Service

```javascript
class CalendarService {
  constructor(googleCalendarClient, cacheService, logger, circuitBreaker) {
    this.googleCalendar = googleCalendarClient;
    this.cacheService = cacheService;
    this.logger = logger;
    this.circuitBreaker = circuitBreaker;
  }

  async createCalendarEvent(meetingData) {
    return await this.circuitBreaker.execute(async () => {
      const event = {
        summary: meetingData.title,
        description: `${meetingData.description}\n\nJoin meeting: ${process.env.FRONTEND_URL}/meetings/${meetingData._id}`,
        start: {
          dateTime: meetingData.scheduledAt.toISOString(),
          timeZone: 'UTC'
        },
        end: {
          dateTime: new Date(meetingData.scheduledAt.getTime() + meetingData.maxDuration * 1000).toISOString(),
          timeZone: 'UTC'
        },
        attendees: meetingData.participants.map(p => ({
          email: p.email,
          responseStatus: 'needsAction'
        })),
        conferenceData: {
          createRequest: {
            requestId: `ms-${meetingData._id}`,
            conferenceSolutionKey: {
              type: 'hangoutsMeet'
            }
          }
        },
        reminders: {
          useDefault: false,
          overrides: [
            { method: 'email', minutes: 15 },
            { method: 'popup', minutes: 5 }
          ]
        }
      };

      const response = await this.googleCalendar.events.insert({
        calendarId: 'primary',
        resource: event,
        conferenceDataVersion: 1,
        sendUpdates: 'all'
      });

      this.logger.info('Calendar event created', {
        eventId: response.data.id,
        meetingId: meetingData._id
      });

      return response.data;
    });
  }

  async updateCalendarEvent(eventId, updates) {
    return await this.circuitBreaker.execute(async () => {
      const response = await this.googleCalendar.events.patch({
        calendarId: 'primary',
        eventId: eventId,
        resource: updates,
        sendUpdates: 'all'
      });

      return response.data;
    });
  }

  async deleteCalendarEvent(eventId) {
    return await this.circuitBreaker.execute(async () => {
      await this.googleCalendar.events.delete({
        calendarId: 'primary',
        eventId: eventId,
        sendUpdates: 'all'
      });

      this.logger.info('Calendar event deleted', { eventId });
    });
  }

  async handleCalendarResponse(eventId, userId, response) {
    // Update meeting participant status based on calendar response
    const meeting = await this.meetingRepository.findByCalendarEventId(eventId);
    if (meeting) {
      const participant = meeting.participants.find(p => p.userId.toString() === userId);
      if (participant) {
        participant.status = response === 'accepted' ? 'accepted' : 'rejected';
        participant.respondedAt = new Date();
        await meeting.save();
      }
    }
  }
}
```

#### Enterprise Recording Service

```javascript
class RecordingService {
  constructor(s3Client, encryptionService, ffmpegService, logger, metrics) {
    this.s3 = s3Client;
    this.encryption = encryptionService;
    this.ffmpeg = ffmpegService;
    this.logger = logger;
    this.metrics = metrics;
  }

  async startRecording(meetingId) {
    try {
      const meeting = await this.meetingRepository.findById(meetingId);
      if (!meeting.canRecord) {
        throw new ForbiddenError('Recording not enabled for this meeting');
      }

      // Start Jitsi recording
      const recordingSession = await this.jitsiService.startRecording(meeting.jitsiRoomId);
      
      // Update meeting with recording session info
      meeting.recording.sessionId = recordingSession.id;
      meeting.recording.status = 'recording';
      await meeting.save();

      this.logger.info('Recording started', {
        meetingId,
        sessionId: recordingSession.id
      });

      return recordingSession;
    } catch (error) {
      this.metrics.recordError('recording_start_failed');
      throw error;
    }
  }

  async stopRecording(meetingId) {
    try {
      const meeting = await this.meetingRepository.findById(meetingId);
      
      // Stop Jitsi recording
      await this.jitsiService.stopRecording(meeting.recording.sessionId);
      
      // Process recording asynchronously
      await this.processRecordingAsync(meetingId);

      this.logger.info('Recording stopped', { meetingId });
    } catch (error) {
      this.metrics.recordError('recording_stop_failed');
      throw error;
    }
  }

  async processRecordingAsync(meetingId) {
    // Queue background job for processing
    await this.taskQueue.add('processRecording', {
      meetingId,
      priority: 'high'
    });
  }

  async processRecording(meetingId) {
    const startTime = Date.now();
    
    try {
      const meeting = await this.meetingRepository.findById(meetingId);
      const rawRecordingPath = await this.downloadRawRecording(meeting.recording.sessionId);
      
      // Compress video
      const compressedPath = await this.compressRecording(rawRecordingPath);
      
      // Generate encryption key
      const encryptionKey = await this.encryption.generateKey();
      
      // Encrypt recording
      const encryptedPath = await this.encryptRecording(compressedPath, encryptionKey);
      
      // Upload to S3
      const s3Key = await this.uploadToS3(encryptedPath, meetingId);
      
      // Update meeting record
      meeting.recording.s3Key = s3Key;
      meeting.recording.s3Bucket = process.env.S3_RECORDINGS_BUCKET;
      meeting.recording.encryptionKey = encryptionKey;
      meeting.recording.fileSize = await this.getFileSize(encryptedPath);
      meeting.recording.status = 'completed';
      await meeting.save();

      // Clean up temporary files
      await this.cleanupTempFiles([rawRecordingPath, compressedPath, encryptedPath]);

      const processingTime = Date.now() - startTime;
      this.metrics.recordProcessingTime('recording_processing', processingTime);
      
      this.logger.info('Recording processed successfully', {
        meetingId,
        s3Key,
        processingTime
      });

    } catch (error) {
      this.logger.error('Recording processing failed', {
        meetingId,
        error: error.message
      });
      
      // Update meeting with error status
      await this.meetingRepository.updateById(meetingId, {
        'recording.status': 'failed',
        'recording.error': error.message
      });
      
      throw error;
    }
  }

  async compressRecording(inputPath) {
    return new Promise((resolve, reject) => {
      const outputPath = `${inputPath}.compressed.mp4`;
      
      this.ffmpeg(inputPath)
        .videoCodec('libx264')
        .audioCodec('aac')
        .videoBitrate('1000k')
        .audioBitrate('128k')
        .size('1280x720')
        .on('end', () => resolve(outputPath))
        .on('error', reject)
        .save(outputPath);
    });
  }

  async encryptRecording(filePath, encryptionKey) {
    const encryptedPath = `${filePath}.encrypted`;
    await this.encryption.encryptFile(filePath, encryptedPath, encryptionKey);
    return encryptedPath;
  }

  async uploadToS3(filePath, meetingId) {
    const s3Key = `recordings/${meetingId}/${Date.now()}.encrypted`;
    
    const uploadParams = {
      Bucket: process.env.S3_RECORDINGS_BUCKET,
      Key: s3Key,
      Body: fs.createReadStream(filePath),
      ServerSideEncryption: 'AES256',
      Metadata: {
        meetingId: meetingId.toString(),
        uploadedAt: new Date().toISOString()
      }
    };

    await this.s3.upload(uploadParams).promise();
    return s3Key;
  }

  async generateDownloadLink(meetingId, userId) {
    const meeting = await this.meetingRepository.findById(meetingId);
    
    if (!await this.validateRecordingAccess(meeting, userId)) {
      throw new ForbiddenError('Access denied to recording');
    }

    // Generate presigned URL
    const downloadUrl = await this.s3.getSignedUrlPromise('getObject', {
      Bucket: meeting.recording.s3Bucket,
      Key: meeting.recording.s3Key,
      Expires: 3600 // 1 hour
    });

    // Log download access
    meeting.recording.downloadCount += 1;
    meeting.recording.lastDownloadedAt = new Date();
    meeting.recording.downloadedBy.push({
      userId,
      downloadedAt: new Date(),
      ipAddress: this.getCurrentUserIP()
    });
    await meeting.save();

    return downloadUrl;
  }

  async validateRecordingAccess(meeting, userId) {
    // Only agency and participants can access recording
    return meeting.agencyId.toString() === userId ||
           meeting.participants.some(p => p.userId.toString() === userId);
  }
}
```

#### Advanced Transcription Service

```javascript
class TranscriptionService {
  constructor(awsTranscribe, azureSpeech, cacheService, logger, metrics) {
    this.awsTranscribe = awsTranscribe;
    this.azureSpeech = azureSpeech;
    this.cacheService = cacheService;
    this.logger = logger;
    this.metrics = metrics;
    this.activeTranscriptions = new Map();
  }

  async startTranscription(meetingId) {
    try {
      const meeting = await this.meetingRepository.findById(meetingId);
      
      // Initialize transcription session
      const transcriptionSession = {
        meetingId,
        startTime: new Date(),
        segments: [],
        speakers: new Map(),
        language: meeting.transcript.language || 'en-US'
      };

      this.activeTranscriptions.set(meetingId, transcriptionSession);

      // Start real-time transcription with primary service
      await this.startAWSTranscription(meetingId, transcriptionSession);

      // Update meeting status
      meeting.transcript.processingStatus = 'processing';
      meeting.transcript.processingStartedAt = new Date();
      await meeting.save();

      this.logger.info('Transcription started', { meetingId });

    } catch (error) {
      this.logger.error('Failed to start transcription', {
        meetingId,
        error: error.message
      });
      throw error;
    }
  }

  async startAWSTranscription(meetingId, session) {
    const params = {
      TranscriptionJobName: `meeting-${meetingId}-${Date.now()}`,
      LanguageCode: session.language,
      MediaFormat: 'wav',
      Media: {
        MediaFileUri: `s3://${process.env.S3_AUDIO_BUCKET}/meetings/${meetingId}/audio.wav`
      },
      OutputBucketName: process.env.S3_TRANSCRIPTS_BUCKET,
      Settings: {
        ShowSpeakerLabels: true,
        MaxSpeakerLabels: 10,
        ShowAlternatives: true,
        MaxAlternatives: 3
      }
    };

    const job = await this.awsTranscribe.startTranscriptionJob(params).promise();
    session.awsJobName = job.TranscriptionJob.TranscriptionJobName;
  }

  async processAudioChunk(audioChunk, meetingId) {
    const session = this.activeTranscriptions.get(meetingId);
    if (!session) {
      return;
    }

    try {
      // Process with Azure Speech for real-time results
      const result = await this.azureSpeech.recognizeOnce(audioChunk, {
        language: session.language,
        enableSpeakerDiarization: true
      });

      if (result.text) {
        const segment = {
          text: result.text,
          timestamp: new Date(),
          confidence: result.confidence,
          speaker: result.speakerId || 'unknown',
          duration: result.duration
        };

        session.segments.push(segment);

        // Emit real-time transcription update
        this.eventBus.emit('transcription.segment', {
          meetingId,
          segment
        });

        // Update speaker mapping
        if (result.speakerId) {
          session.speakers.set(result.speakerId, {
            id: result.speakerId,
            segmentCount: (session.speakers.get(result.speakerId)?.segmentCount || 0) + 1
          });
        }
      }

    } catch (error) {
      this.logger.warn('Real-time transcription chunk failed', {
        meetingId,
        error: error.message
      });
    }
  }

  async finalizeTranscript(meetingId) {
    try {
      const session = this.activeTranscriptions.get(meetingId);
      if (!session) {
        throw new Error('No active transcription session found');
      }

      // Get final AWS transcription results
      const awsResult = await this.getAWSTranscriptionResult(session.awsJobName);
      
      // Merge real-time and final transcription results
      const finalTranscript = await this.mergeTranscriptionResults(
        session.segments,
        awsResult
      );

      // Identify speakers using meeting participant data
      const identifiedTranscript = await this.identifySpeakers(
        finalTranscript,
        meetingId
      );

      // Format and save transcript
      const formattedTranscript = await this.formatTranscript(identifiedTranscript);
      
      // Update meeting with final transcript
      const meeting = await this.meetingRepository.findById(meetingId);
      meeting.transcript.content = formattedTranscript.content;
      meeting.transcript.speakers = formattedTranscript.speakers;
      meeting.transcript.wordCount = formattedTranscript.wordCount;
      meeting.transcript.confidence = formattedTranscript.averageConfidence;
      meeting.transcript.processingStatus = 'completed';
      meeting.transcript.processingCompletedAt = new Date();
      await meeting.save();

      // Clean up session
      this.activeTranscriptions.delete(meetingId);

      // Index transcript for search
      await this.indexTranscriptForSearch(meetingId, formattedTranscript);

      this.logger.info('Transcription finalized', {
        meetingId,
        wordCount: formattedTranscript.wordCount,
        confidence: formattedTranscript.averageConfidence
      });

      return formattedTranscript;

    } catch (error) {
      this.logger.error('Failed to finalize transcription', {
        meetingId,
        error: error.message
      });
      
      // Update meeting with error status
      await this.meetingRepository.updateById(meetingId, {
        'transcript.processingStatus': 'failed'
      });
      
      throw error;
    }
  }

  async identifySpeakers(transcript, meetingId) {
    const meeting = await this.meetingRepository.findById(meetingId);
    const participants = meeting.participants;

    // Use voice recognition or participant mapping logic
    // This is a simplified version - in production, you'd use more sophisticated speaker identification
    
    const speakerMap = new Map();
    let speakerIndex = 0;

    transcript.segments.forEach(segment => {
      if (!speakerMap.has(segment.speaker)) {
        const participant = participants[speakerIndex % participants.length];
        speakerMap.set(segment.speaker, {
          id: participant.userId,
          name: participant.name || 'Unknown Speaker'
        });
        speakerIndex++;
      }
    });

    return {
      ...transcript,
      speakerMap
    };
  }

  async formatTranscript(transcript) {
    const speakers = [];
    const speakerSegments = new Map();

    // Group segments by speaker
    transcript.segments.forEach(segment => {
      const speakerId = segment.speaker;
      if (!speakerSegments.has(speakerId)) {
        speakerSegments.set(speakerId, []);
      }
      speakerSegments.get(speakerId).push(segment);
    });

    // Format speakers array
    speakerSegments.forEach((segments, speakerId) => {
      const speakerInfo = transcript.speakerMap.get(speakerId);
      speakers.push({
        name: speakerInfo?.name || 'Unknown Speaker',
        userId: speakerInfo?.id,
        segments: segments.map(s => ({
          text: s.text,
          timestamp: s.timestamp,
          confidence: s.confidence,
          duration: s.duration
        }))
      });
    });

    // Generate formatted content
    const content = this.generateFormattedContent(transcript.segments, transcript.speakerMap);

    return {
      content,
      speakers,
      wordCount: content.split(' ').length,
      averageConfidence: this.calculateAverageConfidence(transcript.segments)
    };
  }

  generateFormattedContent(segments, speakerMap) {
    let content = '';
    let currentSpeaker = null;

    segments.sort((a, b) => a.timestamp - b.timestamp);

    segments.forEach(segment => {
      const speakerInfo = speakerMap.get(segment.speaker);
      const speakerName = speakerInfo?.name || 'Unknown Speaker';

      if (currentSpeaker !== speakerName) {
        content += `\n\n**${speakerName}** (${segment.timestamp.toLocaleTimeString()}):\n`;
        currentSpeaker = speakerName;
      }

      content += `${segment.text} `;
    });

    return content.trim();
  }

  async indexTranscriptForSearch(meetingId, transcript) {
    // Index in Elasticsearch for full-text search
    await this.elasticsearchClient.index({
      index: 'meeting-transcripts',
      id: meetingId,
      body: {
        meetingId,
        content: transcript.content,
        speakers: transcript.speakers.map(s => s.name),
        wordCount: transcript.wordCount,
        confidence: transcript.averageConfidence,
        indexedAt: new Date()
      }
    });
  }
}
```

### Frontend Components

#### Meeting Dashboard Component
```jsx
const MeetingDashboard = () => {
  // Display upcoming, in-progress, and past meetings
  // Quick actions for creating instant meetings
  // Meeting status indicators and real-time updates
}
```

#### Meeting Creation Form
```jsx
const MeetingForm = ({ type }) => {
  // Form for creating scheduled or instant meetings
  // Participant selection with model search
  // Calendar integration for scheduling
  // Meeting settings (recording, duration, etc.)
}
```

#### Meeting Lobby Component
```jsx
const MeetingLobby = ({ meetingId }) => {
  // Pre-meeting waiting area
  // Participant status display
  // Meeting details and agenda
  // Audio/video test functionality
}
```

#### Jitsi Meet Integration
```jsx
const JitsiMeetComponent = ({ meetingId, jwt }) => {
  // Embedded Jitsi Meet interface
  // Custom branding and UI modifications
  // Event handling for meeting lifecycle
  // Recording controls for agencies
}
```

#### Meeting Summary Component
```jsx
const MeetingSummary = ({ meetingId }) => {
  // Post-meeting summary display
  // Transcript viewer with search
  // Recording download (if available)
  // Feedback form integration
}
```

## Data Models

### Meeting Model
- **Primary Entity**: Stores all meeting information, participants, and metadata
- **Relationships**: Links to Agency, ModelUser, and related services
- **Indexing**: Optimized for queries by agency, participant, date, and status

### Meeting Participant Model
- **Embedded Document**: Within Meeting model for participant tracking
- **Status Tracking**: Invitation, acceptance, attendance, and engagement metrics
- **Role Management**: Distinguishes between organizers and participants

### Meeting Recording Model
- **Security Focus**: Encrypted storage with access controls
- **S3 Integration**: Secure file storage with presigned URLs
- **Audit Trail**: Download tracking and access logging

### Meeting Transcript Model
- **Embedded Document**: Within Meeting model for transcript data
- **Speaker Identification**: Links transcript segments to participants
- **Search Optimization**: Full-text search capabilities for transcript content

## Error Handling

### Meeting Creation Errors
- **Validation Errors**: Invalid participant selection, scheduling conflicts
- **Calendar Errors**: Google Calendar API failures, permission issues
- **Resource Errors**: Maximum meeting limit exceeded, insufficient permissions

### Meeting Runtime Errors
- **Connection Errors**: Network issues, Jitsi server unavailability
- **Authentication Errors**: Invalid JWT, expired tokens, unauthorized access
- **Recording Errors**: Storage failures, encoding issues, insufficient space

### Transcription Errors
- **Processing Errors**: Audio quality issues, language detection failures
- **Storage Errors**: Database write failures, transcript corruption
- **Service Errors**: Transcription service unavailability, API rate limits

### Error Recovery Strategies
- **Graceful Degradation**: Continue meeting without recording/transcription if services fail
- **Retry Mechanisms**: Automatic retry for transient failures with exponential backoff
- **Fallback Options**: Alternative transcription services, local recording options
- **User Notification**: Clear error messages with suggested actions

## Testing Strategy

### Unit Testing
- **Service Layer**: Test all meeting service methods with mocked dependencies
- **Controller Layer**: Test API endpoints with various input scenarios
- **Model Validation**: Test schema validation and data integrity
- **Utility Functions**: Test JWT generation, encryption, and calendar integration

### Integration Testing
- **API Testing**: End-to-end API testing with real database connections
- **External Service Testing**: Test Google Calendar and S3 integrations
- **Socket.IO Testing**: Test real-time notification delivery
- **Database Testing**: Test complex queries and data relationships

### End-to-End Testing
- **Meeting Flow Testing**: Complete meeting lifecycle from creation to completion
- **Multi-User Testing**: Test concurrent meeting participation
- **Recording Testing**: Test recording generation, encryption, and download
- **Transcription Testing**: Test real-time transcription accuracy and storage

### Performance Testing
- **Load Testing**: Test system under multiple concurrent meetings
- **Stress Testing**: Test system limits and failure points
- **Recording Performance**: Test large file handling and S3 upload speeds
- **Transcription Performance**: Test real-time processing capabilities

### Security Testing
- **Authentication Testing**: Test JWT validation and access controls
- **Authorization Testing**: Test role-based permissions and meeting access
- **Data Encryption Testing**: Test recording encryption and secure storage
- **Input Validation Testing**: Test for injection attacks and malformed data

## Jitsi Meet Integration Architecture

### Self-Hosted Jitsi Meet Deployment

```mermaid
graph TB
    subgraph "Jitsi Meet Infrastructure"
        JVB[Jitsi Videobridge]
        JICOFO[Jicofo Conference Focus]
        PROSODY[Prosody XMPP Server]
        JIGASI[Jigasi SIP Gateway]
        JIBRI[Jibri Recording Service]
    end
    
    subgraph "Load Balancer"
        NGINX[NGINX Load Balancer]
        SSL[SSL Termination]
    end
    
    subgraph "ModelSuite Backend"
        JWT_SERVICE[JWT Generation Service]
        MEETING_API[Meeting API]
        WEBHOOK[Jitsi Webhooks Handler]
    end
    
    subgraph "Frontend Integration"
        REACT_APP[React Application]
        JITSI_COMPONENT[Jitsi Meet Component]
        IFRAME[Jitsi Meet iFrame]
    end
    
    REACT_APP --> MEETING_API
    MEETING_API --> JWT_SERVICE
    JWT_SERVICE --> PROSODY
    JITSI_COMPONENT --> NGINX
    NGINX --> SSL
    SSL --> PROSODY
    PROSODY --> JICOFO
    JICOFO --> JVB
    JVB --> JIBRI
    PROSODY --> WEBHOOK
    WEBHOOK --> MEETING_API
```

### Jitsi Meet Service Implementation

```javascript
class JitsiMeetService {
  constructor(jitsiConfig, httpClient, logger) {
    this.config = jitsiConfig;
    this.httpClient = httpClient;
    this.logger = logger;
    this.jitsiDomain = process.env.JITSI_DOMAIN;
    this.jitsiApiUrl = `https://${this.jitsiDomain}/api`;
  }

  async createRoom(meetingId, moderatorId) {
    const roomName = this.generateRoomName(meetingId);
    
    try {
      // Create room via Jitsi API
      const response = await this.httpClient.post(`${this.jitsiApiUrl}/rooms`, {
        name: roomName,
        subject: `ModelSuite Meeting ${meetingId}`,
        password: this.generateRoomPassword(),
        config: {
          startWithAudioMuted: true,
          startWithVideoMuted: false,
          requireDisplayName: true,
          enableWelcomePage: false,
          prejoinPageEnabled: true,
          disableModeratorIndicator: false,
          enableUserRolesBasedOnToken: true,
          enableFeaturesBasedOnToken: true,
          recordingService: {
            enabled: true,
            sharingEnabled: false
          },
          liveStreamingEnabled: false,
          transcribingEnabled: true,
          fileRecordingsEnabled: true,
          localRecording: {
            disable: true
          },
          p2p: {
            enabled: false // Force through JVB for recording
          },
          analytics: {
            disabled: false,
            rtcstatsEnabled: true
          },
          deploymentInfo: {
            shard: 'modelsuite',
            userRegion: 'us-east-1'
          }
        }
      }, {
        headers: {
          'Authorization': `Bearer ${process.env.JITSI_API_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });

      this.logger.info('Jitsi room created', {
        roomName,
        meetingId,
        jitsiRoomId: response.data.id
      });

      return {
        roomName,
        roomId: response.data.id,
        joinUrl: `https://${this.jitsiDomain}/${roomName}`
      };

    } catch (error) {
      this.logger.error('Failed to create Jitsi room', {
        meetingId,
        error: error.message
      });
      throw new Error(`Failed to create video room: ${error.message}`);
    }
  }

  async generateJWT(roomName, userId, userRole, meetingData) {
    const now = Math.floor(Date.now() / 1000);
    const exp = now + 3600; // 1 hour expiration

    const payload = {
      iss: process.env.JITSI_APP_ID,
      sub: this.jitsiDomain,
      aud: 'jitsi',
      exp: exp,
      nbf: now - 10, // Not before (10 seconds ago to account for clock skew)
      room: roomName,
      context: {
        user: {
          id: userId,
          name: await this.getUserDisplayName(userId),
          email: await this.getUserEmail(userId),
          avatar: await this.getUserAvatarUrl(userId)
        },
        group: meetingData.agencyId,
        features: {
          livestreaming: userRole === 'moderator',
          recording: userRole === 'moderator',
          transcription: true,
          'outbound-call': userRole === 'moderator',
          'sip-outbound-call': false,
          'sip-inbound-call': false
        }
      },
      moderator: userRole === 'moderator',
      room_info: {
        name: meetingData.title,
        description: meetingData.description,
        max_participants: meetingData.metadata.maxParticipants
      }
    };

    const token = jwt.sign(payload, process.env.JITSI_SECRET, {
      algorithm: 'HS256',
      header: {
        typ: 'JWT',
        alg: 'HS256',
        kid: process.env.JITSI_KEY_ID
      }
    });

    this.logger.info('JWT generated for Jitsi', {
      userId,
      roomName,
      role: userRole,
      expiresAt: new Date(exp * 1000)
    });

    return token;
  }

  async startRecording(roomName, meetingId) {
    try {
      const response = await this.httpClient.post(`${this.jitsiApiUrl}/recording/start`, {
        room: roomName,
        session_id: `ms_${meetingId}_${Date.now()}`,
        recording_mode: 'file',
        recording_directory: `/recordings/meetings/${meetingId}`,
        layout: 'BEST_FIT',
        resolution: '1280x720',
        framerate: 30,
        audio_only: false,
        sharing_enabled: false
      }, {
        headers: {
          'Authorization': `Bearer ${process.env.JITSI_API_TOKEN}`
        }
      });

      this.logger.info('Jitsi recording started', {
        roomName,
        meetingId,
        sessionId: response.data.session_id
      });

      return response.data;

    } catch (error) {
      this.logger.error('Failed to start Jitsi recording', {
        roomName,
        meetingId,
        error: error.message
      });
      throw error;
    }
  }

  async stopRecording(sessionId) {
    try {
      const response = await this.httpClient.post(`${this.jitsiApiUrl}/recording/stop`, {
        session_id: sessionId
      }, {
        headers: {
          'Authorization': `Bearer ${process.env.JITSI_API_TOKEN}`
        }
      });

      this.logger.info('Jitsi recording stopped', { sessionId });
      return response.data;

    } catch (error) {
      this.logger.error('Failed to stop Jitsi recording', {
        sessionId,
        error: error.message
      });
      throw error;
    }
  }

  async destroyRoom(roomName) {
    try {
      await this.httpClient.delete(`${this.jitsiApiUrl}/rooms/${roomName}`, {
        headers: {
          'Authorization': `Bearer ${process.env.JITSI_API_TOKEN}`
        }
      });

      this.logger.info('Jitsi room destroyed', { roomName });

    } catch (error) {
      this.logger.warn('Failed to destroy Jitsi room', {
        roomName,
        error: error.message
      });
      // Don't throw - room cleanup is not critical
    }
  }

  async handleWebhook(webhookData) {
    const { event_type, room_name, participant_id, timestamp } = webhookData;

    switch (event_type) {
      case 'room-created':
        await this.handleRoomCreated(room_name, timestamp);
        break;
      case 'room-destroyed':
        await this.handleRoomDestroyed(room_name, timestamp);
        break;
      case 'participant-joined':
        await this.handleParticipantJoined(room_name, participant_id, timestamp);
        break;
      case 'participant-left':
        await this.handleParticipantLeft(room_name, participant_id, timestamp);
        break;
      case 'recording-started':
        await this.handleRecordingStarted(room_name, webhookData.session_id);
        break;
      case 'recording-stopped':
        await this.handleRecordingStopped(room_name, webhookData.session_id, webhookData.file_path);
        break;
      default:
        this.logger.warn('Unknown webhook event', { event_type, room_name });
    }
  }

  generateRoomName(meetingId) {
    return `ms_meeting_${meetingId}_${crypto.randomBytes(8).toString('hex')}`;
  }

  generateRoomPassword() {
    return crypto.randomBytes(16).toString('hex');
  }
}
```

### Frontend Jitsi Integration

```jsx
// Jitsi Meet React Component
import { JitsiMeeting } from '@jitsi/react-sdk';

const JitsiMeetComponent = ({ meetingId, jwtToken, onMeetingEnd, onParticipantJoined }) => {
  const [jitsiApi, setJitsiApi] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('connecting');

  const jitsiConfig = {
    roomName: `ms_meeting_${meetingId}`,
    jwt: jwtToken,
    configOverwrite: {
      startWithAudioMuted: true,
      startWithVideoMuted: false,
      enableWelcomePage: false,
      prejoinPageEnabled: true,
      requireDisplayName: true,
      disableModeratorIndicator: false,
      toolbarButtons: [
        'microphone', 'camera', 'closedcaptions', 'desktop', 'fullscreen',
        'fodeviceselection', 'hangup', 'profile', 'chat', 'recording',
        'livestreaming', 'etherpad', 'sharedvideo', 'settings', 'raisehand',
        'videoquality', 'filmstrip', 'invite', 'feedback', 'stats', 'shortcuts',
        'tileview', 'videobackgroundblur', 'download', 'help', 'mute-everyone'
      ],
      disableDeepLinking: true,
      defaultLanguage: 'en',
      enableUserRolesBasedOnToken: true,
      enableFeaturesBasedOnToken: true,
      recordingService: {
        enabled: true,
        sharingEnabled: false
      },
      liveStreamingEnabled: false,
      fileRecordingsEnabled: true,
      localRecording: {
        disable: true
      },
      transcribingEnabled: true,
      analytics: {
        disabled: false
      },
      p2p: {
        enabled: false
      }
    },
    interfaceConfigOverwrite: {
      BRAND_WATERMARK_LINK: process.env.REACT_APP_FRONTEND_URL,
      DEFAULT_BACKGROUND: '#1a1a1a',
      DISABLE_JOIN_LEAVE_NOTIFICATIONS: false,
      DISPLAY_WELCOME_PAGE_CONTENT: false,
      ENABLE_FEEDBACK_ANIMATION: true,
      FILM_STRIP_MAX_HEIGHT: 120,
      GENERATE_ROOMNAMES_ON_WELCOME_PAGE: false,
      HIDE_INVITE_MORE_HEADER: false,
      JITSI_WATERMARK_LINK: process.env.REACT_APP_FRONTEND_URL,
      LANG_DETECTION: true,
      MOBILE_APP_PROMO: false,
      NATIVE_APP_NAME: 'ModelSuite',
      PROVIDER_NAME: 'ModelSuite',
      RECENT_LIST_ENABLED: false,
      REMOTE_THUMBNAIL_MENU_BUTTON_MAX_WIDTH: 25,
      SETTINGS_SECTIONS: ['devices', 'language', 'moderator', 'profile', 'calendar'],
      SHOW_BRAND_WATERMARK: true,
      SHOW_CHROME_EXTENSION_BANNER: false,
      SHOW_JITSI_WATERMARK: false,
      SHOW_POWERED_BY: false,
      SHOW_PROMOTIONAL_CLOSE_PAGE: false,
      TOOLBAR_ALWAYS_VISIBLE: false,
      TOOLBAR_TIMEOUT: 4000
    },
    userInfo: {
      displayName: 'Loading...' // Will be updated via JWT
    }
  };

  const handleApiReady = (apiObj) => {
    setJitsiApi(apiObj);
    setConnectionStatus('connected');

    // Set up event listeners
    apiObj.addEventListeners({
      participantJoined: (participant) => {
        onParticipantJoined?.(participant);
      },
      participantLeft: (participant) => {
        // Handle participant leaving
      },
      videoConferenceJoined: (participant) => {
        setConnectionStatus('joined');
      },
      videoConferenceLeft: () => {
        onMeetingEnd?.();
      },
      recordingStatusChanged: (status) => {
        // Handle recording status changes
      },
      transcriptionStatusChanged: (status) => {
        // Handle transcription status changes
      }
    });
  };

  const handleReadyToClose = () => {
    onMeetingEnd?.();
  };

  return (
    <div className="jitsi-container" style={{ height: '100vh', width: '100%' }}>
      {connectionStatus === 'connecting' && (
        <div className="connection-status">
          <div className="spinner"></div>
          <p>Connecting to meeting...</p>
        </div>
      )}
      
      <JitsiMeeting
        domain={process.env.REACT_APP_JITSI_DOMAIN}
        roomName={jitsiConfig.roomName}
        jwt={jitsiConfig.jwt}
        configOverwrite={jitsiConfig.configOverwrite}
        interfaceConfigOverwrite={jitsiConfig.interfaceConfigOverwrite}
        userInfo={jitsiConfig.userInfo}
        onApiReady={handleApiReady}
        onReadyToClose={handleReadyToClose}
        getIFrameRef={(iframeRef) => {
          iframeRef.style.height = '100%';
          iframeRef.style.width = '100%';
        }}
      />
    </div>
  );
};

export default JitsiMeetComponent;
```

### Jitsi Deployment Configuration

```yaml
# docker-compose.yml for Jitsi Meet
version: '3.8'

services:
  # Frontend
  web:
    image: jitsi/web:stable
    restart: unless-stopped
    ports:
      - '80:80'
      - '443:443'
    volumes:
      - ${CONFIG}/web:/config:Z
      - ${CONFIG}/web/letsencrypt:/etc/letsencrypt:Z
      - ${CONFIG}/transcripts:/usr/share/jitsi-meet/transcripts:Z
    environment:
      - ENABLE_AUTH=1
      - ENABLE_GUESTS=0
      - AUTH_TYPE=jwt
      - JWT_APP_ID=${JITSI_APP_ID}
      - JWT_APP_SECRET=${JITSI_SECRET}
      - JWT_ACCEPTED_ISSUERS=${JITSI_APP_ID}
      - JWT_ACCEPTED_AUDIENCES=jitsi
      - JWT_ASAP_KEYSERVER=${JITSI_KEYSERVER_URL}
      - JWT_ALLOW_EMPTY=0
      - JWT_AUTH_TYPE=token
      - JWT_TOKEN_AUTH_MODULE=token_verification
      - ENABLE_RECORDING=1
      - DROPBOX_APPKEY=${DROPBOX_APPKEY}
      - DROPBOX_REDIRECT_URL=${DROPBOX_REDIRECT_URL}
      - ENABLE_TRANSCRIPTIONS=1
      - DEPLOY_ENV=production
    depends_on:
      - prosody
      - jicofo
      - jvb

  # XMPP server
  prosody:
    image: jitsi/prosody:stable
    restart: unless-stopped
    expose:
      - '5222'
      - '5347'
      - '5280'
    volumes:
      - ${CONFIG}/prosody/config:/config:Z
      - ${CONFIG}/prosody/prosody-plugins-custom:/prosody-plugins-custom:Z
    environment:
      - AUTH_TYPE=jwt
      - JWT_APP_ID=${JITSI_APP_ID}
      - JWT_APP_SECRET=${JITSI_SECRET}
      - JWT_ACCEPTED_ISSUERS=${JITSI_APP_ID}
      - JWT_ACCEPTED_AUDIENCES=jitsi
      - JWT_ASAP_KEYSERVER=${JITSI_KEYSERVER_URL}
      - JWT_ALLOW_EMPTY=0
      - JWT_AUTH_TYPE=token
      - JWT_TOKEN_AUTH_MODULE=token_verification
      - ENABLE_AUTH=1
      - ENABLE_GUESTS=0
      - GLOBAL_MODULES=token_verification
      - GLOBAL_CONFIG=cross_domain_bosh = false;
      - LDAP_URL=${LDAP_URL}
      - LDAP_BASE=${LDAP_BASE}
      - ENABLE_LOBBY=1
      - ENABLE_XMPP_WEBSOCKET=1
      - PUBLIC_URL=https://${JITSI_DOMAIN}

  # Focus component
  jicofo:
    image: jitsi/jicofo:stable
    restart: unless-stopped
    volumes:
      - ${CONFIG}/jicofo:/config:Z
    environment:
      - AUTH_TYPE=jwt
      - JWT_APP_ID=${JITSI_APP_ID}
      - JWT_APP_SECRET=${JITSI_SECRET}
      - JWT_ACCEPTED_ISSUERS=${JITSI_APP_ID}
      - JWT_ACCEPTED_AUDIENCES=jitsi
      - JWT_ASAP_KEYSERVER=${JITSI_KEYSERVER_URL}
      - JWT_ALLOW_EMPTY=0
      - JWT_AUTH_TYPE=token
      - JWT_TOKEN_AUTH_MODULE=token_verification
      - ENABLE_AUTH=1
      - ENABLE_RECORDING=1
    depends_on:
      - prosody

  # Video bridge
  jvb:
    image: jitsi/jvb:stable
    restart: unless-stopped
    ports:
      - '10000:10000/udp'
      - '4443:4443'
    volumes:
      - ${CONFIG}/jvb:/config:Z
    environment:
      - DOCKER_HOST_ADDRESS=${DOCKER_HOST_ADDRESS}
      - ENABLE_COLIBRI_WEBSOCKET=1
      - JVB_PORT=10000
      - JVB_TCP_HARVESTER_DISABLED=true
      - JVB_TCP_PORT=4443
      - JVB_TCP_MAPPED_PORT=4443
      - JVB_STUN_SERVERS=meet-jit-si-turnrelay.jitsi.net:443
    depends_on:
      - prosody

  # Recording service
  jibri:
    image: jitsi/jibri:stable
    restart: unless-stopped
    volumes:
      - ${CONFIG}/jibri:/config:Z
      - /dev/shm:/dev/shm
      - ${RECORDINGS_DIR}:/tmp/recordings:Z
    cap_add:
      - SYS_ADMIN
    devices:
      - /dev/snd:/dev/snd
    environment:
      - ENABLE_RECORDING=1
      - JIBRI_RECORDER_USER=${JIBRI_RECORDER_USER}
      - JIBRI_RECORDER_PASSWORD=${JIBRI_RECORDER_PASSWORD}
      - JIBRI_RECORDING_DIR=/tmp/recordings
      - JIBRI_FINALIZE_RECORDING_SCRIPT_PATH=/config/finalize.sh
      - JIBRI_XMPP_USER=${JIBRI_XMPP_USER}
      - JIBRI_XMPP_PASSWORD=${JIBRI_XMPP_PASSWORD}
      - JIBRI_BREWERY_MUC=${JIBRI_BREWERY_MUC}
      - JIBRI_RECORDER_MUC=${JIBRI_RECORDER_MUC}
      - JIBRI_LOGS_DIR=/config/logs
    depends_on:
      - jicofo
```

This comprehensive Jitsi integration provides:

1. **Self-hosted Jitsi Meet** with full control over the infrastructure
2. **JWT-based authentication** integrated with your user system
3. **Custom branding** and UI configuration
4. **Recording capabilities** with automatic file processing
5. **Transcription support** for meeting minutes
6. **Webhook integration** for real-time event handling
7. **Production-ready deployment** with Docker containers
8. **Security features** including room passwords and access controls

The integration ensures that all video meeting functionality is seamlessly embedded within your ModelSuite platform while maintaining full control over the infrastructure and user experience.

## Security Considerations

### Authentication and Authorization
- **JWT-based Authentication**: Secure token-based access for all meeting operations
- **Role-based Access Control**: Different permissions for agencies and models
- **Meeting-specific Tokens**: Time-limited tokens for Jitsi room access
- **API Rate Limiting**: Prevent abuse and ensure fair resource usage

### Data Protection
- **Encryption at Rest**: All recordings encrypted before S3 storage
- **Encryption in Transit**: HTTPS for all API communications, secure WebRTC
- **Access Logging**: Comprehensive audit trail for all meeting and recording access
- **Data Retention**: Configurable retention policies for recordings and transcripts

### Meeting Security
- **Room Access Control**: Only authenticated participants can join meetings
- **Time-limited Sessions**: Automatic meeting termination after time limits
- **Secure Room IDs**: Cryptographically secure room identifier generation
- **Participant Validation**: Continuous validation of participant authenticity

## Performance Optimization

### Database Optimization
- **Indexing Strategy**: Optimized indexes for common query patterns
- **Query Optimization**: Efficient aggregation pipelines for meeting analytics
- **Connection Pooling**: Optimized database connection management
- **Caching Strategy**: Redis caching for frequently accessed meeting data

### Real-time Performance
- **Socket.IO Optimization**: Efficient event handling and room management
- **Connection Management**: Optimized WebSocket connection handling
- **Message Queuing**: Asynchronous processing for non-critical operations
- **Load Balancing**: Distributed architecture for high availability

### Storage and Bandwidth
- **Recording Compression**: Efficient video compression before storage
- **CDN Integration**: Fast content delivery for recordings and transcripts
- **Streaming Optimization**: Efficient audio streaming for transcription
- **Bandwidth Management**: Quality adaptation based on connection speed

## Deployment Architecture

### Production Infrastructure
- **Containerized Deployment**: Docker containers for all services
- **Microservices Architecture**: Separate containers for different services
- **Load Balancing**: NGINX load balancer for high availability
- **Auto-scaling**: Kubernetes-based scaling for meeting demand

### External Service Integration
- **Jitsi Meet Deployment**: Self-hosted Jitsi with custom configuration
- **S3 Configuration**: Secure bucket configuration with proper IAM roles
- **Google Calendar Setup**: OAuth2 configuration for calendar integration
- **Transcription Service**: Integration with speech-to-text APIs

### Monitoring and Logging
- **Application Monitoring**: Comprehensive logging for all meeting operations
- **Performance Monitoring**: Real-time performance metrics and alerting
- **Error Tracking**: Centralized error logging and notification system
- **Usage Analytics**: Meeting usage patterns and system performance metrics