# Implementation Plan

## Phase 1: Meeting Dependencies and Environment Setup

- [x] 1.1 Install meeting-specific dependencies

  - Add jsonwebtoken for Jitsi JWT generation (already installed)
  - Install @jitsi/react-sdk for frontend integration
  - Add aws-sdk for S3 and transcription services
  - Install bull and redis for job queuing
  - Add ffmpeg-static for video processing
  - _Requirements: 3.1, 4.1, 5.1_

- [x] 1.2 Add meeting environment variables

  - Add Jitsi Meet configuration variables to .env.example
  - Add AWS S3 and transcription service credentials
  - Add Google Calendar API configuration
  - Add meeting-specific security keys and settings
  - _Requirements: 3.2, 4.3, 5.1, 6.1_

- [x] 1.3 Create meeting services folder structure

  - Create modelsuite-backend/services/meeting/ directory
  - Create modelsuite-backend/services/jitsi/ directory
  - Create modelsuite-backend/services/recording/ directory
  - Create modelsuite-backend/services/transcription/ directory
  - Create modelsuite-backend/services/calendar/ directory
  - _Requirements: Architecture organization_

- [x] 1.4 Set up Redis connection for caching and queues
  - Create modelsuite-backend/config/redis.js connection utility
  - Configure Redis client with connection pooling
  - Add Redis health check to existing health endpoint
  - _Requirements: Performance and job queuing_

## Phase 2: Core Data Models

- [x] 2.1 Create basic Meeting model schema

  - Define Meeting schema with basic fields (title, description, agencyId)
  - Add basic validation for required fields
  - Create Meeting model export
  - _Requirements: 1.3_

- [x] 2.2 Add participant management to Meeting model

  - Add participants array with userId, role, status fields
  - Implement participant validation logic
  - Add participant status enum values
  - _Requirements: 1.2, 2.1_

- [x] 2.3 Add meeting lifecycle fields to Meeting model

  - Add type, status, scheduledAt, startedAt, endedAt fields
  - Implement meeting status enum validation
  - Add duration calculation virtual field
  - _Requirements: 1.1, 1.6_

- [x] 2.4 Add recording fields to Meeting model

  - Add recording object with enabled, s3Key, fileSize fields
  - Implement recording validation logic
  - Add recording access control fields
  - _Requirements: 4.1, 4.3_

- [x] 2.5 Add transcript fields to Meeting model

  - Add transcript object with content, speakers, processingStatus
  - Implement transcript validation and speaker structure
  - Add full-text search index for transcript content
  - _Requirements: 5.1, 5.2, 5.4_

- [x] 2.6 Add metadata and audit fields to Meeting model
  - Add metadata object with maxParticipants, createdBy, version
  - Implement audit log array for tracking changes
  - Add timestamps and model indexes for performance
  - _Requirements: 8.4, 9.7_

## Phase 3: Basic API Structure

- [ ] 3.1 Create Meeting controller following existing patterns

  - Create modelsuite-backend/controllers/meetingController.js
  - Follow existing controller pattern from agencyController.js
  - Add basic CRUD method stubs (create, get, update, delete)
  - Implement error handling following existing patterns
  - _Requirements: 1.1_

- [ ] 3.2 Implement createMeeting controller method

  - Add input validation for meeting creation
  - Implement meeting creation logic with error handling
  - Return proper JSON response following existing response patterns
  - Use existing user context from req.user (from verifyToken middleware)
  - _Requirements: 1.1, 1.3_

- [ ] 3.3 Implement getMeetings controller method

  - Add query parameter parsing for filtering
  - Implement basic meeting list retrieval with user filtering
  - Add pagination support with limit and offset
  - Filter meetings based on user role (agency vs model)
  - _Requirements: 1.7_

- [ ] 3.4 Implement getMeetingById controller method

  - Add meeting ID validation using mongoose ObjectId
  - Implement single meeting retrieval with error handling
  - Add access control check using user role and meeting ownership
  - _Requirements: 1.7_

- [ ] 3.5 Create Meeting routes following existing patterns
  - Create modelsuite-backend/routes/meetingRoutes.js
  - Follow existing route pattern from agencyRoutes.js
  - Add POST /api/v1/meetings route for creation
  - Add GET /api/v1/meetings route for listing
  - Add GET /api/v1/meetings/:id route for single meeting
  - Integrate routes in server.js following existing pattern
  - _Requirements: 1.1, 1.7_

## Phase 4: Meeting-Specific Authorization

- [ ] 4.1 Create meeting access control utilities

  - Create modelsuite-backend/utils/meetingAuth.js utility file
  - Implement meeting ownership validation (agency can create/manage)
  - Add participant access validation (models can join assigned meetings)
  - _Requirements: 9.1, 9.3_

- [ ] 4.2 Add meeting permission middleware

  - Create meeting-specific permission checking middleware
  - Implement canCreateMeeting, canJoinMeeting, canManageMeeting functions
  - Add meeting visibility controls based on user role
  - _Requirements: 9.1, 9.2_

- [ ] 4.3 Integrate existing auth with meeting routes
  - Use existing verifyToken middleware for meeting routes
  - Add meeting-specific authorization checks to routes
  - Implement user context filtering for meeting queries
  - _Requirements: 9.1, 9.2_

## Phase 5: Meeting Service Layer

- [ ] 5.1 Create basic MeetingService class

  - Create MeetingService class with constructor
  - Add dependency injection for database models
  - Implement basic service method structure
  - _Requirements: 1.1_

- [ ] 5.2 Implement meeting creation service logic

  - Add business rule validation for meeting creation
  - Implement participant limit and validation checks
  - Add meeting conflict detection for scheduled meetings
  - _Requirements: 1.1, 1.2, 1.3_

- [ ] 5.3 Implement meeting lifecycle service methods

  - Add startMeeting method with status validation
  - Implement joinMeeting with participant tracking
  - Add endMeeting method with cleanup logic
  - _Requirements: 1.6, 2.2, 2.5_

- [ ] 5.4 Add meeting participant management
  - Implement addParticipant and removeParticipant methods
  - Add participant status update functionality
  - Implement participant invitation logic
  - _Requirements: 1.2, 2.1, 2.3_

## Phase 6: Jitsi Meet Integration

- [ ] 6.1 Create basic JitsiService class

  - Create JitsiService class with configuration
  - Add JWT generation utility for Jitsi authentication
  - Implement room name generation logic
  - _Requirements: 3.1, 3.2_

- [ ] 6.2 Implement JWT token generation for Jitsi

  - Add JWT signing with Jitsi-specific claims
  - Implement user context and role mapping
  - Add token expiration and security features
  - _Requirements: 3.2, 9.1_

- [ ] 6.3 Add room creation and management

  - Implement createRoom method with Jitsi API integration
  - Add room configuration with custom settings
  - Implement destroyRoom method for cleanup
  - _Requirements: 3.1, 3.5_

- [ ] 6.4 Integrate Jitsi service with meeting lifecycle
  - Add Jitsi room creation to meeting start process
  - Implement JWT generation for meeting participants
  - Add room cleanup to meeting end process
  - _Requirements: 3.1, 3.2, 3.5_

## Phase 7: Recording Functionality

- [ ] 7.1 Create basic RecordingService class

  - Create RecordingService class with S3 configuration
  - Add basic recording start/stop method stubs
  - Implement recording file path generation
  - _Requirements: 4.1_

- [ ] 7.2 Implement recording start functionality

  - Add recording initiation with Jitsi integration
  - Implement recording session tracking
  - Add recording status updates to meeting model
  - _Requirements: 4.1, 4.2_

- [ ] 7.3 Implement recording stop and processing

  - Add recording stop functionality
  - Implement basic file processing pipeline
  - Add recording metadata storage
  - _Requirements: 4.2, 4.3_

- [ ] 7.4 Add S3 upload functionality

  - Implement S3 file upload with proper configuration
  - Add file encryption before upload
  - Implement secure file naming and organization
  - _Requirements: 4.3, 4.5_

- [ ] 7.5 Create recording download functionality
  - Implement secure download link generation
  - Add access control for recording downloads
  - Implement download tracking and audit logging
  - _Requirements: 4.4, 4.5_

## Phase 8: Transcription Service

- [ ] 8.1 Create basic TranscriptionService class

  - Create TranscriptionService class with AWS Transcribe config
  - Add transcription job management methods
  - Implement basic audio processing utilities
  - _Requirements: 5.1_

- [ ] 8.2 Implement transcription start functionality

  - Add transcription job creation with AWS Transcribe
  - Implement real-time transcription session tracking
  - Add transcription status updates to meeting model
  - _Requirements: 5.1, 5.2_

- [ ] 8.3 Add transcript processing and formatting

  - Implement transcript result processing
  - Add speaker identification and formatting
  - Create readable transcript generation
  - _Requirements: 5.3, 5.4, 5.6_

- [ ] 8.4 Integrate transcription with meeting lifecycle
  - Add transcription start to meeting start process
  - Implement transcript finalization on meeting end
  - Add transcript storage and retrieval
  - _Requirements: 5.1, 5.2, 5.4_

## Phase 9: Calendar Integration

- [ ] 9.1 Create basic CalendarService class

  - Create CalendarService class with Google Calendar config
  - Add Google Calendar API client setup
  - Implement basic calendar event methods
  - _Requirements: 6.1_

- [ ] 9.2 Implement calendar event creation

  - Add calendar event creation for scheduled meetings
  - Implement participant invitation via calendar
  - Add meeting details and join links to calendar events
  - _Requirements: 6.1, 6.2_

- [ ] 9.3 Add calendar event management

  - Implement calendar event updates for meeting changes
  - Add calendar event deletion for cancelled meetings
  - Implement reminder scheduling
  - _Requirements: 6.3, 6.4_

- [ ] 9.4 Integrate calendar service with meeting lifecycle
  - Add calendar event creation to scheduled meeting creation
  - Implement calendar updates for meeting status changes
  - Add calendar cleanup for completed meetings
  - _Requirements: 6.1, 6.4, 6.6_

## Phase 10: Real-time Meeting Notifications

- [ ] 10.1 Create meeting socket handlers

  - Create modelsuite-backend/sockets/meetingSocket.js handler file
  - Add meeting room join/leave functionality
  - Implement meeting-specific socket event handlers
  - Integrate with existing socket system in sockets/index.js
  - _Requirements: 7.1_

- [ ] 10.2 Create MeetingNotificationService class

  - Create modelsuite-backend/services/meeting/notificationService.js
  - Add meeting notification broadcasting methods using existing Socket.IO
  - Implement meeting invitation and status change notifications
  - Use existing connectedUsers Map for user targeting
  - _Requirements: 7.1, 7.6_

- [ ] 10.3 Implement meeting status notifications

  - Add real-time notifications for meeting status changes (started, ended)
  - Implement participant join/leave notifications
  - Add meeting invitation and response notifications
  - Emit events to specific meeting rooms and participants
  - _Requirements: 7.1, 7.2, 7.4_

- [ ] 10.4 Integrate notifications with meeting lifecycle
  - Add notification triggers to MeetingService methods
  - Implement notification delivery to relevant users via Socket.IO
  - Add meeting room management for real-time updates
  - _Requirements: 7.1, 7.2, 7.5_

## Phase 11: Frontend Meeting Dashboard

- [ ] 11.1 Create basic React meeting dashboard component

  - Set up React component with basic structure
  - Add meeting list display with basic styling
  - Implement loading states and error handling
  - _Requirements: 10.1, 10.2_

- [ ] 11.2 Add meeting creation form

  - Create meeting creation form with input validation
  - Add participant selection and invitation interface
  - Implement form submission with API integration
  - _Requirements: 1.1, 1.2_

- [ ] 11.3 Implement meeting list with filtering

  - Add meeting status filtering (upcoming, completed, etc.)
  - Implement search functionality for meetings
  - Add pagination for large meeting lists
  - _Requirements: 1.7, 8.6_

- [ ] 11.4 Add real-time updates to dashboard
  - Integrate Socket.IO client for real-time updates
  - Implement meeting status updates in real-time
  - Add participant status tracking in UI
  - _Requirements: 7.1, 7.2, 10.2_

## Phase 12: Jitsi Meet Frontend Integration

- [ ] 12.1 Install and configure Jitsi Meet React SDK

  - Install @jitsi/react-sdk package
  - Set up basic Jitsi Meet component structure
  - Configure Jitsi domain and basic settings
  - _Requirements: 3.1, 10.1_

- [ ] 12.2 Create JitsiMeetComponent with authentication

  - Implement JWT token integration for Jitsi authentication
  - Add user context and role-based permissions
  - Implement meeting join functionality
  - _Requirements: 3.2, 3.4_

- [ ] 12.3 Add custom branding and UI configuration

  - Implement ModelSuite branding in Jitsi interface
  - Configure custom toolbar and interface elements
  - Add meeting-specific configurations
  - _Requirements: 3.4, 10.5_

- [ ] 12.4 Implement meeting controls and participant management
  - Add recording start/stop controls for moderators
  - Implement participant management interface
  - Add meeting end functionality
  - _Requirements: 3.1, 4.1, 10.6_

## Phase 13: Post-Meeting Features

- [ ] 13.1 Create meeting summary component

  - Build meeting summary display with basic information
  - Add participant list and meeting duration
  - Implement meeting status and outcome display
  - _Requirements: 8.2, 8.4_

- [ ] 13.2 Add transcript viewer

  - Create transcript display component with formatting
  - Implement speaker identification in transcript view
  - Add transcript search functionality
  - _Requirements: 5.4, 5.5_

- [ ] 13.3 Implement recording download interface

  - Add recording availability check and display
  - Implement secure download link generation
  - Add download access control and tracking
  - _Requirements: 4.4, 4.5_

- [ ] 13.4 Create feedback collection system
  - Build feedback form for meeting participants
  - Implement rating system and comment collection
  - Add feedback submission and storage
  - _Requirements: 8.1, 8.2_

## Phase 14: Testing and Quality Assurance

- [ ] 14.1 Set up Jest testing framework

  - Configure Jest for Node.js backend testing
  - Set up test database configuration
  - Add basic test utilities and helpers
  - _Requirements: Testing infrastructure_

- [ ] 14.2 Create unit tests for models and services

  - Write unit tests for Meeting model validation
  - Add tests for MeetingService business logic
  - Create tests for JitsiService and other utilities
  - _Requirements: All service layer requirements_

- [ ] 14.3 Add integration tests for API endpoints

  - Create integration tests for meeting CRUD operations
  - Add tests for authentication and authorization
  - Implement tests for meeting lifecycle endpoints
  - _Requirements: All API requirements_

- [ ] 14.4 Create end-to-end tests for complete workflows
  - Write e2e tests for complete meeting creation to completion
  - Add tests for recording and transcription workflows
  - Implement tests for participant management flows
  - _Requirements: All workflow requirements_

## Phase 15: Deployment and Production Setup

- [ ] 15.1 Create Docker configuration for backend

  - Write Dockerfile for Node.js application
  - Create docker-compose.yml for local development
  - Add environment variable configuration
  - _Requirements: 9.4_

- [ ] 15.2 Set up Jitsi Meet Docker deployment

  - Configure Jitsi Meet Docker containers
  - Set up JWT authentication configuration
  - Add custom branding and configuration
  - _Requirements: 3.1, 3.2_

- [ ] 15.3 Configure production environment variables

  - Set up production environment configuration
  - Add secrets management for sensitive data
  - Configure database and external service connections
  - _Requirements: 9.2, 9.4_

- [ ] 15.4 Add monitoring and health checks
  - Implement health check endpoints for all services
  - Add basic logging and error monitoring
  - Configure application monitoring and alerts
  - _Requirements: Performance and reliability_
