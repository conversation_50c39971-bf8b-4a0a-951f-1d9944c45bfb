# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.1.0/),  
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

---

## [Unreleased]

---

## [0.0.2] - 2025-07-16

### Fixed
- Resolved merge conflicts and cleaned up codebase.

---

### Added
- Initial setup for changelog tracking.

---

## [0.0.1] - 2025-07-15

### Added
- Model and agency authentication system with JWT.
- File upload using Multer and Cloudinary.
- Versioned routes with `/api/v1` structure.
- Real-time group chat system using Socket.IO.
- Group/topic/message architecture with default fallback logic.
- Protected routes and role-based access system.
- Some initial setup is done .

---

[Unreleased]: https://github.com/modelsuite-ai/modelsuite-backend/compare/0.0.2...HEAD  
[0.0.2]: https://github.com/modelsuite-ai/modelsuite-backend/releases/tag/0.0.2
[0.0.1]: https://github.com/modelsuite-ai/modelsuite-backend/releases/tag/0.0.1
