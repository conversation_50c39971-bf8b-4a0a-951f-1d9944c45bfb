// PDF Configuration
export const pdfConfig = {
  contactInfo: {
    email: '<EMAIL>',
    phone: '+****************', 
    website: 'www.modelsuite.ai'
  },
  
  // Add your logo as base64 string here
  // To convert: https://base64.guru/converter/encode/image
  logo: {
    base64: '', // Add your base64 logo string here
    fallback: 'MS' // Fallback text if no logo provided
  },
  
  branding: {
    companyName: 'ModelSuite.ai',
    primaryColor: '#64ffda',
    secondaryColor: '#2B3990'
  }
};

// Update contact info easily
export const updateContactInfo = (newContactInfo) => {
  Object.assign(pdfConfig.contactInfo, newContactInfo);
};

// Update logo
export const updateLogo = (base64String) => {
  pdfConfig.logo.base64 = base64String;
};
