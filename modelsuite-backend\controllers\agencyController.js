import Agency from "../models/agency.js";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import ModelUser from "../models/model.js";
import { sendOtpToEmail } from "../utils/sendOtpToEmail.js";

import Otp from "../models/otp.js";
import { randomInt } from "crypto";

async function generateOtp() {
  return randomInt(100000, 999999).toString();
}

export const generateAccessAndRefreshTokens = async (userId) => {
  try {
    const user = await Agency.findById(userId);

    const accessToken = user.generateAccessToken();
    const refreshToken = user.generateRefreshToken();

    user.loginRefreshToken = refreshToken;

    await user.save({ validateBeforeSave: false });

    return { accessToken, refreshToken };
  } catch (error) {
    console.error("Error generating tokens:", error);
    throw new Error(
      "Something went wrong while generating Access and Refresh Tokens."
    );
  }
};

export const sendOtpForRegistration = async (req, res) => {
  try {
    const {
      agencyName,
      agencyEmail,

      username,
      password,
      confirmPassword,
    } = req.body;

    // Required fields check
    if (
      !agencyName ||
      !agencyEmail ||
      !username ||
      !password ||
      !confirmPassword
    ) {
      return res.status(400).json({ error: "Required fields missing" });
    }

    if (password !== confirmPassword) {
      return res.status(400).json({ error: "Passwords do not match" });
    }

    if (
      password.length < 8 ||
      !/\d/.test(password) ||
      !/[a-zA-Z]/.test(password)
    ) {
      return res
        .status(400)
        .json({ error: "Password must be 8+ chars with letters & numbers" });
    }

    // Uniqueness checks
    const dataExists = await Agency.findOne({
      $or: [
        agencyEmail ? { agencyEmail } : null,
        username ? { username } : null,
      ].filter(Boolean),
    });

    if (dataExists) {
      return res
        .status(409)
        .json({ error: "Email or Username already exists" });
    }

    const expiresAt = new Date(Date.now() + 10 * 60 * 1000);
    const userData = {
      agencyName,
      agencyEmail,
      username,
      password,
    };
    // Send OTP to email
    if (agencyEmail) {
      await Otp.deleteMany({ email: agencyEmail }); //old otps must be deleted

      const emailOtp = await generateOtp();
      await Otp.create({
        email: agencyEmail,
        otp: emailOtp,
        expiresAt,
        userData,
        type: "email",
      });
      try {
        await sendOtpToEmail(agencyEmail, emailOtp);
      } catch (error) {
        return res.status(500).json({ error: "Email OTP could not be sent." });
      }
    } else {
      return res.status(400).json({ message: "Otp could not be sent." });
    }

    res.status(200).json({
      message:
        "OTP has been sent successfully. Please verify your credentials for a successful registration.",
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Server error" });
  }
};

export const verifyOtpAndRegister = async (req, res) => {
  try {
    const { agencyEmail, otp } = req.body;

    const otpRecord = await Otp.findOne({
      $or: [agencyEmail ? { email: agencyEmail, otp } : null].filter(Boolean),
    });

    if (!otpRecord || otpRecord.expiresAt < new Date()) {
      return res.status(400).json({ error: "Invalid or expired OTP" });
    }

    const {
      agencyName,
      agencyEmail: userEmail,
      agencyPhone: userPhone,
      username,
      password,
    } = otpRecord.userData;

    await Otp.deleteOne({ _id: otpRecord._id });
    const hashedPassword = await bcrypt.hash(password, 10);
    const isPhoneVerified = false;
    const isEmailVerified = !!agencyEmail;

    const newAgency = new Agency({
      agencyName,
      agencyEmail: userEmail,
      agencyPhone: userPhone,
      username,
      password: hashedPassword,
      isPhoneVerified,
      isEmailVerified,
    });

    await newAgency.save();

    const { accessToken, refreshToken } = await generateAccessAndRefreshTokens(
      newAgency._id
    );

    const user = {
      _id: newAgency._id,
      agencyName: newAgency.agencyName,
      agencyEmail: newAgency.agencyEmail,
      username: newAgency.username,
      isPhoneVerified: newAgency.isPhoneVerified,
      isEmailVerified: newAgency.isEmailVerified,
    };

    const options = {
      httpOnly: true,
      secure: true,
    };

    res
      .cookie("accessToken", accessToken, options)
      .cookie("refreshToken", refreshToken, options);

    return res
      .status(200)
      .json({ message: "Registered successfully", user, token: accessToken });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Server error" });
  }
};

export const loginAgency = async (req, res) => {
  try {
    const { identifier, password } = req.body;

    if (!identifier || !password)
      if (!identifier || !password)
        return res
          .status(400)
          .json({ error: "Either email or phone and password are required" });

    const agency = await Agency.findOne({
      $or: [{ agencyEmail: identifier }, { username: identifier }],
    });

    if (!agency) {
      return res.status(401).json({ error: "Invalid credentials" });
    }
    if (!agency) return res.status(401).json({ error: "Invalid credentials" });

    const isMatch = await bcrypt.compare(password, agency.password);
    if (!isMatch) {
      return res.status(401).json({ error: "Invalid credentials" });
    }

    if (!isMatch) return res.status(401).json({ error: "Invalid credentials" });

    // MFA logic
    if (agency.isMfaActive) {
      const otp = await generateOtp();
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000);

      let otpDeleteQuery = {};
      if (identifier.includes("@")) {
        otpDeleteQuery.email = identifier;
      } else {
        otpDeleteQuery["userData.username"] = identifier;
      }

      if (agency.agencyEmail) {
        await Otp.create({
          email: agency.agencyEmail,
          email: agency.agencyEmail,
          otp,
          expiresAt,
          userData: {
            username: agency.username,
            agencyEmail: agency.agencyEmail,
          },
          type: "email",
        });
        await sendOtpToEmail(agency.agencyEmail, otp);
        return res.status(200).json({ message: "OTP sent to email" });
      }
    }

    // // Direct login if MFA is inactive
    // const token = jwt.sign(
    //   { id: agency._id, role: agency.role },
    //   process.env.JWT_SECRET,
    //   { expiresIn: "7d" }
    // );
    const { accessToken, refreshToken } = await generateAccessAndRefreshTokens(
      agency._id
    );
    const safeUser = {
      _id: agency._id,
      agencyName: agency.agencyName,
      agencyEmail: agency.agencyEmail,
      username: agency.username,
      role: agency.role,
      profilePhoto: agency.profilePhoto,
      city: agency.city,
      phone: agency.agencyPhone,
    };

    const options = {
      httpOnly: true,
      secure: true,
    };

    res
      .cookie("accessToken", accessToken, options)
      .cookie("refreshToken", refreshToken, options);

    return res.status(200).json({
      message: "Login successful",
      user: safeUser,
      token: accessToken,
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Server error" });
  }
};

export const verifyOtpAndLoginAgency = async (req, res) => {
  try {
    const { identifier, otp } = req.body;

    if (!identifier || !otp) {
      return res.status(400).json({ error: "Identifier and OTP are required" });
    }

    const query = {
      otp,
      $or: [{ email: identifier }, { "userData.username": identifier }],
    };
    const otpRecord = await Otp.findOne(query);

    if (!otpRecord || otpRecord.expiresAt < new Date()) {
      return res.status(400).json({ error: "Invalid or expired OTP" });
    }

    const agency = await Agency.findOne({
      $or: [{ agencyEmail: identifier }, { username: identifier }],
    });

    if (!agency) {
      return res.status(404).json({ error: "Agency not found" });
    }

    await Otp.deleteOne({ _id: otpRecord._id });

    const { accessToken, refreshToken } = await generateAccessAndRefreshTokens(
      agency._id
    );
    const safeUser = {
      _id: agency._id,
      agencyName: agency.agencyName,
      agencyEmail: agency.agencyEmail,
      username: agency.username,
      agencyPhone: agency.agencyPhone,
    };

    const options = {
      httpOnly: true,
      secure: true,
    };

    res
      .cookie("accessToken", accessToken, options)
      .cookie("refreshToken", refreshToken, options);

    return res.status(200).json({
      message: "Login successful",
      user: safeUser,
      token: accessToken,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: "Server error" });
  }
};

export const logoutUser = async (req, res) => {
  await Agency.findByIdAndUpdate(
    req.user._id,
    {
      $set: {
        refreshToken: undefined,
      },
    },
    {
      new: true,
    }
  );

  const options = {
    httpOnly: true,
    secure: true,
  };

  return res
    .status(200)
    .clearCookie("accessToken", options)
    .clearCookie("refreshToken", options)
    .json({ message: "Logged out successfully!" });
};

export const searchModels = async (req, res) => {
  const searchQuery = req.query.search;
  const regex = new RegExp(searchQuery, "i"); // case-insensitive

  const results = await ModelUser.find({
    $or: [{ fullName: regex }, { username: regex }],
  }).select("-password -confirmPassword");

  res.status(200).json(results);
};

export const getAgencyModels = async (req, res) => {
  try {
    const agencyId = req.user._id; // from token
    console.log('Getting models for agency:', agencyId);
    
    const models = await ModelUser.find({ agencyId }).select(
      "-password -confirmPassword"
    );
    
    console.log('Found models:', models.length);
    res.status(200).json(models);
  } catch (err) {
    console.error('Error in getAgencyModels:', err);
    res.status(500).json({ error: "Failed to fetch agency models" });
  }
};

// Debug endpoint to see all models and their assignment status
export const debugModels = async (req, res) => {
  try {
    const agencyId = req.user._id;
    
    // Get all models
    const allModels = await ModelUser.find({}).select(
      "-password -confirmPassword"
    );
    
    // Get models assigned to this agency
    const assignedModels = await ModelUser.find({ agencyId }).select(
      "-password -confirmPassword"
    );
    
    // Get unassigned models
    const unassignedModels = await ModelUser.find({ 
      $or: [{ agencyId: null }, { agencyId: { $exists: false } }] 
    }).select("-password -confirmPassword");
    
    res.status(200).json({
      agencyId,
      totalModels: allModels.length,
      assignedModels: assignedModels.length,
      unassignedModels: unassignedModels.length,
      allModels,
      assignedToThisAgency: assignedModels,
      unassigned: unassignedModels
    });
  } catch (err) {
    console.error('Error in debugModels:', err);
    res.status(500).json({ error: "Failed to debug models" });
  }
};

// Bulk assign all unassigned models to current agency
export const assignAllModels = async (req, res) => {
  try {
    const agencyId = req.user._id;
    
    // Find all unassigned models
    const unassignedModels = await ModelUser.find({ 
      $or: [{ agencyId: null }, { agencyId: { $exists: false } }] 
    });
    
    // Assign them to this agency
    const updateResult = await ModelUser.updateMany(
      { $or: [{ agencyId: null }, { agencyId: { $exists: false } }] },
      { $set: { agencyId } }
    );
    
    console.log(`Assigned ${updateResult.modifiedCount} models to agency ${agencyId}`);
    
    res.status(200).json({
      message: `Successfully assigned ${updateResult.modifiedCount} models to your agency`,
      modifiedCount: updateResult.modifiedCount,
      totalUnassigned: unassignedModels.length
    });
  } catch (err) {
    console.error('Error in assignAllModels:', err);
    res.status(500).json({ error: "Failed to assign models" });
  }
};

export const addModelToAgency = async (req, res) => {
  try {
    const agencyId = req.user._id; // comes from token
    const { modelId } = req.body;

    if (!modelId) {
      return res.status(400).json({ error: "Model ID is required" });
    }

    // Check if model exists
    const model = await ModelUser.findById(modelId);
    if (!model) {
      return res.status(404).json({ error: "Model not found" });
    }

    // Optional: Check if already assigned
    if (model.agencyId && model.agencyId.toString() === agencyId) {
      return res.status(400).json({ error: "Model already in your agency" });
    }

    // Assign agencyId
    model.agencyId = agencyId;
    await model.save();

    res
      .status(200)
      .json({ message: "Model added to agency successfully", model });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Server error" });
  }
};

export const toggleMfa = async (req, res) => {
  try {
    const userId = req.user._id; // assuming you're using auth middleware to attach user info

    const user = await Agency.findById(userId);
    if (!user) return res.status(404).json({ error: "User not found" });

    user.isMfaActive = !user.isMfaActive;
    await user.save();

    res.status(200).json({
      message: `MFA is now ${user.isMfaActive ? "enabled" : "disabled"}`,
      isMfaActive: user.isMfaActive,
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Server error" });
  }
};

export const deleteAgency = async (req, res) => {
  // just a utility func for now to re-register with same credentials
  const { agencyEmail, agencyPhone } = req.body;
  if (!agencyEmail && !agencyPhone)
    return res.status(404).json({ error: "Invalid request." });
  try {
    // Find the user by email or phone
    const user = await Agency.findOne({
      $or: [
        agencyEmail ? { agencyEmail } : null,
        agencyPhone ? { agencyPhone } : null,
      ].filter(Boolean),
    });

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Delete the user
    await Agency.deleteOne({ _id: user._id });

    res.status(200).json({ message: "Agency deleted successfully" });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Server error" });
  }
};

export const forgotPasswordSendOtp = async (req, res) => {
  const { identifier, newPassword, confirmPassword } = req.body;

  if (!identifier || !newPassword || !confirmPassword) {
    return res.status(400).json({ error: "Required fields missing" });
  }

  if (newPassword !== confirmPassword) {
    return res.status(400).json({ error: "Passwords do not match" });
  }

  if (
    newPassword.length < 8 ||
    !/\d/.test(newPassword) ||
    !/[a-zA-Z]/.test(newPassword)
  ) {
    return res
      .status(400)
      .json({ error: "Password must be 8+ charswith letters & numbers" });
  }

  const user = await Agency.findOne({
    $or: [{ agencyEmail: identifier }, { username: identifier }],
  });

  if (!user)
    return res
      .status(400)
      .json({ error: "No account found. Invalid credentials." });

  let platform = null;
  let contact = null;

  if (user.agencyEmail === identifier || user.username === identifier) {
    if (!user.isEmailVerified || !user.agencyEmail) {
      return res
        .status(400)
        .json({ error: "Email is not verified. Cannot reset password." });
    }

    platform = "email";
    contact = user.agencyEmail;
  } else {
    return res
      .status(400)
      .json({ error: "No verified email found for this account" });
  }
  // Generate OTP
  const otp = await generateOtp();
  const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 mins
  const userData = { userId: user._id, newPassword };

  if (platform === "email") {
    await Otp.deleteMany({ email: contact });
  } else {
    await Otp.deleteMany({ phone: contact });
  }
  if (platform === "email") {
    await Otp.deleteMany({ email: contact });
  } else {
    await Otp.deleteMany({ phone: contact });
  }

  // Save OTP
  await Otp.create({
    email: platform === "email" ? contact : undefined,
    // phone: platform === 'phone' ? contact : undefined,
    otp,
    expiresAt,
    userData,
    type: platform,
  });
  // Save OTP
  await Otp.create({
    email: platform === "email" ? contact : undefined,
    // phone: platform === 'phone' ? contact : undefined,
    otp,
    expiresAt,
    userData,
    type: platform,
  });

  try {
    if (platform === "email") {
      await sendOtpToEmail(contact, otp);
    }
    return res.status(200).json({
      message: `OTP sent to your ${platform}. Please verify to reset your password.`,
    });
  } catch (error) {
    return res
      .status(500)
      .json({ error: `Failed to send OTP via ${platform}` });
  }
};

export const forgotPasswordVerifyOtp = async (req, res) => {
  const { identifier, otp } = req.body;

  if (!identifier || !otp) {
    return res.status(400).json({ error: "Identifier and OTP are required" });
  }

  let user = await Agency.findOne({
    $or: [{ agencyEmail: identifier }, { username: identifier }],
  });

  if (!user) {
    return res.status(404).json({ error: "User not found" });
  }

  const otpEntry = await Otp.findOne({
    email: user.agencyEmail,
    otp,
  });

  if (!otpEntry) {
    return res.status(400).json({ error: "Invalid OTP or Email" });
  }

  // Check expiry
  if (otpEntry.expiresAt < new Date()) {
    await Otp.deleteOne({ _id: otpEntry._id });
    return res.status(400).json({ error: "OTP has expired" });
  }

  const { userId, newPassword } = otpEntry.userData;

  if (!userId || !newPassword) {
    return res.status(500).json({ error: "OTP data corrupted or incomplete" });
  }

  // Hash the new password
  const hashedPassword = await bcrypt.hash(newPassword, 10);

  // Update the user
  user = await Agency.findByIdAndUpdate(
    userId,
    { password: hashedPassword },
    { new: true }
  );

  if (!user) {
    return res.status(404).json({ error: "User not found" });
  }

  // Clean up used OTP
  await Otp.deleteOne({ _id: otpEntry._id });
  return res
    .status(200)
    .json({ message: "Password has been reset successfully" });
};

export const getAgencyDetails = async (req, res) => {
  try {
    const agencyId = req.user.id; // Assuming JWT middleware sets `req.user`

    const agency = await Agency.findById(agencyId).select("-password");

    if (!agency) {
      return res.status(404).json({ message: "Agency not found" });
    }

    res.status(200).json({ agency });
  } catch (error) {
    console.error("Error fetching agency details:", error);
    res.status(500).json({ message: "Server error" });
  }
};

export const updateAgencyDetails = async (req, res) => {
  const agencyId = req.user.id;
  let { socialLinks, ctaButtons, whyUs, specialties, ...otherFields } =
    req.body;

  try {
    if (req.files) {
      if (req.files["logo"] && req.files["logo"][0]) {
        otherFields.logo = req.files["logo"][0].path;
      }
      if (req.files["bannerImage"] && req.files["bannerImage"][0]) {
        otherFields.bannerImage = req.files["bannerImage"][0].path;
      }
      if (req.files["trustBadge"] && req.files["trustBadge"][0]) {
        otherFields.trustBadge = req.files["trustBadge"][0].path;
      }
    }

    if (req.body.teamMembers) {
      let teamMembers;

      // Parse team members if it's a JSON string
      if (typeof req.body.teamMembers === "string") {
        teamMembers = JSON.parse(req.body.teamMembers);
      } else {
        teamMembers = req.body.teamMembers;
      }

      // Process team member avatars
      if (req.files && teamMembers) {
        teamMembers.forEach((member, index) => {
          const avatarFieldName = `teamMemberAvatar${index}`;
          if (req.files[avatarFieldName]) {
            member.avatar = req.files[avatarFieldName][0].path; // or your file handling logic
          }
        });
      }

      otherFields.team = teamMembers;
    }

    // Parse the JSON string back to object
    if (typeof socialLinks === "string") {
      try {
        socialLinks = JSON.parse(socialLinks);
      } catch (parseError) {
        return res.status(400).json({
          error: "Invalid socialLinks JSON format",
        });
      }
    }

    if (typeof whyUs === "string") {
      try {
        whyUs = JSON.parse(whyUs);
      } catch (parseError) {
        return res.status(400).json({
          error: "Invalid socialLinks JSON format",
        });
      }
    }

    if (typeof specialties === "string") {
      try {
        specialties = JSON.parse(specialties);
      } catch (parseError) {
        return res.status(400).json({
          error: "Invalid socialLinks JSON format",
        });
      }
    }

    if (typeof ctaButtons === "string") {
      try {
        ctaButtons = JSON.parse(ctaButtons);
      } catch (parseError) {
        return res.status(400).json({
          error: "Invalid socialLinks JSON format",
        });
      }
    }

    const updateData = {
      ...otherFields,
      ctaButtons: ctaButtons,
      socialLinks: socialLinks,
      specialties: specialties,
      whyUs: whyUs,
    };

    const updatedAgency = await Agency.findByIdAndUpdate(agencyId, updateData, {
      new: true,
      runValidators: true,
    }).select("-password -confirmPassword");

    if (!updatedAgency) {
      return res.status(404).json({ error: "Agency not found" });
    }

    res.status(200).json({
      message: "Agency details updated successfully",
      agency: updatedAgency,
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Failed to update agency details" });
  }
};

// Add these new controller functions

// Get all custom inserts for an agency
export const getCustomInserts = async (req, res) => {
  try {
    const agency = await Agency.findById(req.user._id);
    if (!agency) {
      return res.status(404).json({ message: 'Agency not found' });
    }

    res.json(agency.customInserts || []);
  } catch (error) {
    console.error('Error fetching custom inserts:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Update custom inserts for an agency
export const updateCustomInserts = async (req, res) => {
  try {
    const { customInserts } = req.body;
    
    const agency = await Agency.findById(req.user._id);
    if (!agency) {
      return res.status(404).json({ message: 'Agency not found' });
    }

    agency.customInserts = customInserts;
    await agency.save();

    res.json(agency.customInserts);
  } catch (error) {
    console.error('Error updating custom inserts:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Update model order
export const updateModelOrder = async (req, res) => {
  try {
    const { modelOrder } = req.body;
    
    const agency = await Agency.findById(req.user._id);
    if (!agency) {
      return res.status(404).json({ message: 'Agency not found' });
    }

    // Update each model with its new order index
    const updatePromises = modelOrder.map(({ id, orderIndex }) => 
      ModelUser.findOneAndUpdate(
        { _id: id, agencyId: req.user._id },
        { $set: { orderIndex } },
        { new: true }
      )
    );

    await Promise.all(updatePromises);

    // Save the order in agency's modelOrder field
    agency.modelOrder = modelOrder.map(item => item.id);
    await agency.save();

    res.json({ 
      message: 'Model order updated successfully', 
      modelOrder: agency.modelOrder 
    });
  } catch (error) {
    console.error('Error updating model order:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Add these controller functions at the end of the file

// Update model group assignment
export const updateModelGroupAssignment = async (req, res) => {
  try {
    const { modelId, groupId } = req.body;
    
    // Find agency and populate model assignments
    const agency = await Agency.findById(req.user._id);
    if (!agency) {
      return res.status(404).json({ 
        success: false, 
        message: 'Agency not found' 
      });
    }

    // Initialize modelGroupAssignments if it doesn't exist
    if (!agency.modelGroupAssignments) {
      agency.modelGroupAssignments = [];
    }

    // Validate group exists (except for ungrouped)
    if (groupId !== 'ungrouped') {
      const groupExists = agency.modelGroups.some(group => group.id === groupId);
      if (!groupExists) {
        return res.status(400).json({ 
          success: false, 
          message: 'Invalid group ID' 
        });
      }
    }

    // Validate model belongs to agency
    const model = await ModelUser.findOne({ 
      _id: modelId, 
      agencyId: req.user._id 
    });
    
    if (!model) {
      return res.status(404).json({ 
        success: false, 
        message: 'Model not found or does not belong to agency' 
      });
    }

    // Remove any existing assignment for this model
    agency.modelGroupAssignments = agency.modelGroupAssignments.filter(
      assignment => assignment.modelId.toString() !== modelId.toString()
    );

    // Add new assignment if not ungrouped
    if (groupId !== 'ungrouped') {
      const newAssignment = {
        modelId: model._id,
        groupId,
        orderInGroup: agency.modelGroupAssignments.filter(a => a.groupId === groupId).length
      };
      agency.modelGroupAssignments.push(newAssignment);
    }

    // Save the agency document with the updated assignments
    await agency.save();

    // Create assignments object for frontend
    const assignmentsObject = {};
    agency.modelGroupAssignments.forEach(assignment => {
      assignmentsObject[assignment.modelId.toString()] = assignment.groupId;
    });

    // Add ungrouped models to assignments object
    const agencyModels = await ModelUser.find({ agencyId: req.user._id });
    agencyModels.forEach(model => {
      if (!assignmentsObject[model._id.toString()]) {
        assignmentsObject[model._id.toString()] = 'ungrouped';
      }
    });

    // Log the assignments for debugging
    console.log('Saved assignments:', assignmentsObject);

    res.json({
      success: true,
      assignments: assignmentsObject,
      updatedModel: {
        _id: model._id,
        fullName: model.fullName,
        username: model.username,
        profilePhoto: model.profilePhoto
      }
    });
  } catch (error) {
    console.error('Error updating model group assignment:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Server error',
      error: error.message 
    });
  }
};

// Get model groups and assignments
export const getModelGroups = async (req, res) => {
  try {
    const agency = await Agency.findById(req.user._id);
    if (!agency) {
      return res.status(404).json({ 
        success: false, 
        message: 'Agency not found' 
      });
    }

    // Initialize default groups if none exist
    if (!agency.modelGroups || agency.modelGroups.length === 0) {
      agency.modelGroups = [
        { id: 'vip', name: 'VIP Models', isCollapsed: false, order: 0 },
        { id: 'attention', name: 'Needs Attention', isCollapsed: false, order: 1 },
        { id: 'inactive', name: 'Inactive', isCollapsed: false, order: 2 },
        { id: 'ungrouped', name: 'Ungrouped', isCollapsed: false, order: 3 }
      ];
      await agency.save();
    }

    // Initialize modelGroupAssignments if it doesn't exist
    if (!agency.modelGroupAssignments) {
      agency.modelGroupAssignments = [];
      await agency.save();
    }

    // Create assignments object including unassigned models
    const assignmentsObject = {};
    agency.modelGroupAssignments.forEach(assignment => {
      assignmentsObject[assignment.modelId.toString()] = assignment.groupId;
    });

    // Add ungrouped models
    const agencyModels = await ModelUser.find({ agencyId: req.user._id });
    agencyModels.forEach(model => {
      if (!assignmentsObject[model._id.toString()]) {
        assignmentsObject[model._id.toString()] = 'ungrouped';
      }
    });

    // Log the assignments for debugging
    console.log('Retrieved assignments:', assignmentsObject);

    res.json({
      success: true,
      groups: agency.modelGroups,
      assignments: assignmentsObject
    });
  } catch (error) {
    console.error('Error fetching model groups:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Server error',
      error: error.message 
    });
  }
};

// Update model groups
export const updateModelGroups = async (req, res) => {
  try {
    const { groups } = req.body;
    
    if (!Array.isArray(groups)) {
      return res.status(400).json({ 
        success: false, 
        message: 'Groups must be an array' 
      });
    }

    // Validate group structure
    const invalidGroups = groups.filter(group => !group.id || !group.name);
    if (invalidGroups.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'All groups must have id and name properties'
      });
    }

    // Ensure required groups exist
    const requiredGroups = ['ungrouped'];
    const missingGroups = requiredGroups.filter(
      required => !groups.some(group => group.id === required)
    );

    if (missingGroups.length > 0) {
      return res.status(400).json({
        success: false,
        message: `Required groups missing: ${missingGroups.join(', ')}`
      });
    }

    const agency = await Agency.findById(req.user._id);
    if (!agency) {
      return res.status(404).json({ 
        success: false, 
        message: 'Agency not found' 
      });
    }

    agency.modelGroups = groups;
    await agency.save();

    res.json({
      success: true,
      groups: agency.modelGroups
    });
  } catch (error) {
    console.error('Error updating model groups:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Server error',
      error: error.message 
    });
  }
};

export const changeAgencyEmail = async(req, res) => {
  try {
    const {agencyEmail} = req.body;

    if(!agencyEmail)
      return res.status(401).json({message: "Email is required."});

    if (!agencyEmail) {
      return res.status(400).json({ message: "Email is required." });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(agencyEmail)) {
      return res.status(400).json({ message: "Please provide a valid email address." });
    }

  
    const emailExists = await Agency.findOne({agencyEmail});
  
    if(emailExists)
      return res.status(401).json({message: "This email already exists."});
  
    const expiresAt = new Date(Date.now() + 10*60*1000) //10minutes
  
    await Otp.deleteMany({email: agencyEmail});
  
    const emailOtp = await generateOtp();
    const userData = {
      agencyEmail
    }
  
    await Otp.create({
      email: agencyEmail,
      otp: emailOtp,
      expiresAt,
      userData,
      type: "email"
    })
    try {
          await sendOtpToEmail(agencyEmail, emailOtp);
    } catch (error) {
          return res.status(500).json({ error: "Email OTP could not be sent." });
    }

    return res.status(200).json({
      message:
        "OTP has been sent successfully. Please verify your new email.",
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: "Server error" });
  }
}

export const verifyNewAgencyEmail = async(req, res) => {
  try {
    const userId = req.user._id;
    const { agencyEmail, otp } = req.body;

    const otpRecord = await Otp.findOne({
      email: agencyEmail,
      otp: otp
    });
    if (!otpRecord || otpRecord.expiresAt < new Date()) {
      return res.status(400).json({ error: "Invalid or expired OTP" });
    }
    
    await Otp.deleteOne({ _id: otpRecord._id });
    const agency = await Agency.findByIdAndUpdate(
      userId, 
      { agencyEmail: agencyEmail },
      { 
        new: true,
        select: "-password -loginRefreshToken"
      }
    );
    if (!agency) {
      return res.status(404).json({ error: "Agency not found." });
    }
    return res.status(200).json({
      message: "Your Email has been changed successfully.",
      agency: {
        _id: agency._id,
        agencyName: agency.agencyName,
        agencyEmail: agency.agencyEmail,
        username: agency.username
      }
    },);

  } catch (error) {
    console.error(error);
    return res.status(500).json({error: "Server error."})
  }
}

export const changePasswordSendOtp = async(req, res) => {
  try {
    const userId = req.user._id;
    const { newPassword, confirmPassword } = req.body;

    if(!newPassword || !confirmPassword)
      return res.status(400).json({ error: "Both Passwords are required." });

    if (newPassword !== confirmPassword) 
      return res.status(400).json({ error: "Passwords do not match" });
    
    if (
      newPassword.length < 8 ||
      !/\d/.test(newPassword) ||
      !/[a-zA-Z]/.test(newPassword)
    ) {
      return res
        .status(400)
        .json({ error: "Password must be 8+ charswith letters & numbers" });
    }

    const agency = await Agency.findById(userId);
    if(!agency)
      return res.status(500).json({message : "Something went wrong in the server."})

    const otp = await generateOtp();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 mins
    const userData = { userId, newPassword };

    await Otp.deleteMany({email: agency.agencyEmail});

    await Otp.create({
      email: agency.agencyEmail,
      otp,
      expiresAt,
      userData,
      type: "email"
    });
      try {
      
        await sendOtpToEmail(agency.agencyEmail, otp);
        return res.status(200).json({
        message: `OTP sent to your Email(${agency.agencyEmail}). Please verify to reset your password.`,
      });
      } catch (error) {
        return res
          .status(500)
          .json({ error: `Failed to send OTP via ${platform}` });
      }
  } catch (error) {
    console.log("Error:", error);
    return res.status(500).json({message: "Server error."})
  }
}

export const verifyOtpAndChangePassword = async(req, res) => {
try {
    const userId = req.user._id;
    const {otp} = req.body
    const agency = await Agency.findById(userId);

    if(!agency)
      return res.status(401).json({message: "unauthorized access."});

    const agencyEmail = agency.agencyEmail;

    const otpRecord = await Otp.findOne({
      email: agencyEmail,
      otp
    })

    if(!otpRecord || otpRecord.expiresAt < new Date())
      return res.status(401).json({message: "Invalid or expired otp."});

    const { newPassword } = otpRecord.userData;

    if (!newPassword) {
      return res.status(500).json({ error: "OTP data corrupted or incomplete" });
    }
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    try {
      await Agency.findByIdAndUpdate(
        userId,
        { password: hashedPassword },
        { new: true }
      );
    } catch (error) {
        return res.status(500).json({message: "Password could not be changed"});
    }

    await Otp.deleteOne({ _id: otpRecord._id });

     return res
    .status(200)
    .json({ message: "Password has been reset successfully" });


  } catch (error) {
    console.log(error);
    return res.status(500).json({message: "Server error"})
  }
}

export const refreshAccessToken = async (req, res) => {
  const incomingRefreshToken =
    req.cookies.refreshToken || req.body.refreshToken;

  if (!incomingRefreshToken)
    return res.status(401).json({ message: "Unauthroized Request." });

  try {
    const decodedToken = jwt.verify(
      incomingRefreshToken,
      process.env.LOGIN_REFRESH_TOKEN_SECRET
    );

    const agency = await Agency.findById(decodedToken?._id);

    if (!agency)
      return res.status(401).json({ message: "Invalid Refresh Token." });

    if (incomingRefreshToken !== agency?.loginRefreshToken)
      return res.status(401).json({ message: "Refresh Token is expired." });

    const { accessToken, refreshToken } = await generateAccessAndRefreshTokens(
      agency._id
    );

    // console.log("Access:", accessToken)
    // console.log("Refresh:", refreshToken)
    const options = {
      httpOnly: true,
      secure: true,
    };

    res
      .cookie("accessToken", accessToken, options)
      .cookie("refreshToken", refreshToken, options);

    return res.status(200).json({
      message: "Tokens refreshed successfully.",
      accessToken,
      refreshToken,
    });
  } catch (error) {
    return res.status(401).json({
      message:
        error?.message ||
        "Something went wrong while generating new Access Token."
    });
  }
};