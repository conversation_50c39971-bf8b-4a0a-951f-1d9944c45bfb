import Invoice from "../../models/billing/invoice.js";
import fs from "fs";
import csv from "fast-csv"


export const uploadInvoice = async (req, res) => {
  try {
    const {
      client,
      campaign,
      amount,
      currency,
      dueDate,
      status,
      note,
    } = req.body;

    // File validation
    if (!req.file) {
      return res.status(400).json({ success: false, message: "Invoice file is required." });
    }

    //  Required field validation
    const missingFields = [];
    if (!client) missingFields.push("client");
    if (!amount) missingFields.push("amount");
    if (!currency) missingFields.push("currency");
    if (!dueDate) missingFields.push("dueDate");
    if (!status) missingFields.push("status");

    if (missingFields.length > 0) {
      return res.status(400).json({
        success: false,
        message: `Missing required fields: ${missingFields.join(", ")}`,
      });
    }

    // Force file to be downloadable from Cloudinary
    const downloadableFileUrl = req.file.path.replace("/upload/", "/upload/fl_attachment/");

    const invoice = new Invoice({
      client,
      campaign,
      amount,
      currency,
      dueDate,
      status,
      note,
      fileUrl: downloadableFileUrl,
      uploadedBy: req.user.id,
    });

    await invoice.save();

    res.status(201).json({
      success: true,
      message: "Invoice uploaded successfully.",
      invoice,
    });
  } catch (err) {
    console.error("Upload Invoice Error:", err);
    res.status(500).json({
      success: false,
      message: "Server error while uploading invoice.",
    });
  }
};

export const getInvoices = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const filter = { uploadedBy: req.user.id };

    // ✅ Optional status filter
    if (req.query.status) {
      filter.status = req.query.status; // should be "Paid", "Unpaid", or "Overdue"
    }

    const totalInvoices = await Invoice.countDocuments(filter);

    const invoices = await Invoice.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate({
        path: "client",
        select: "fullName profilePhoto username _id",
      })
      .populate({
        path: "uploadedBy",
        select: "agencyName agencyEmail username _id",
      });

    res.status(200).json({
      success: true,
      page,
      totalPages: Math.ceil(totalInvoices / limit),
      totalInvoices,
      invoices,
    });
  } catch (err) {
    console.error("Get Invoices Error:", err);
    res.status(500).json({
      success: false,
      message: "Server error while fetching invoices.",
    });
  }
};
export const updateInvoice = async (req, res) => {
  try {
    const invoiceId = req.params.id;
    const agencyId = req.user._id; // Logged-in user's ID

    // Allowed fields to update
    const allowedUpdates = ["client", "campaign", "amount", "currency", "dueDate", "status", "note"];
    const updateData = {};
 

    // Only include fields present in request
    allowedUpdates.forEach((field) => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    // Now perform secure update
    const updatedInvoice = await Invoice.findOneAndUpdate(
      { _id: invoiceId, uploadedBy: agencyId }, // Only update if agency matches
      updateData,
      { new: true } // Return updated document (optional, can be false if you don't need it)
    );

    if (!updatedInvoice) {
      return res.status(404).json({
        success: false,
        message: "Invoice not found or unauthorized.",
      });
    }

    res.status(200).json({
      success: true,
      message: "Invoice updated successfully.",
    });

  } catch (error) {
    console.error("Update Invoice Error:", error);
    res.status(500).json({
      success: false,
      message: "Something went wrong while updating invoice.",
    });
  }
};

export const deleteInvoice = async (req, res) => {
  try {
    const invoiceId = req.params.id;
    const userId = req.user._id; 
    
    
    const deletedInvoice = await Invoice.findOneAndDelete({
      _id: invoiceId,
       uploadedBy: userId,
    });

    if (!deletedInvoice) {
      return res.status(404).json({
        success: false,
        message: "Invoice not found or not authorized to delete.",
      });
    }

    res.status(200).json({
      success: true,
      message: "Invoice deleted successfully.",
    });
  } catch (error) {
    console.error("Delete Invoice Error:", error);
    res.status(500).json({
      success: false,
      message: "Something went wrong while deleting invoice.",
    });
  }
};

export const getInvoiceById = async (req, res) => {
  try {
    const invoiceId = req.params.id;
    const userId = req.user._id; // From auth middleware

    
    const invoice = await Invoice.findOne({
      _id: invoiceId,
      uploadedBy: userId,
    }).populate({
        path: "client",
        select: "fullName profilePhoto username _id",
      })
      .populate({
        path: "uploadedBy",
        select: "agencyName agencyEmail username _id",
      });;

    if (!invoice) {
      return res.status(404).json({
        success: false,
        message: "Invoice not found or not authorized to access.",
      });
    }

    res.status(200).json({
      success: true,
      data: invoice,
    });
  } catch (error) {
    console.error("Get Invoice Error:", error);
    res.status(500).json({
      success: false,
      message: "Something went wrong while fetching invoice.",
    });
  }
};

export const exportInvoices = async (req, res) => {
  try {
    const userId = req.user._id;

    const invoices = await Invoice.find({ uploadedBy: userId })
      .populate({
        path: "client",
        select: "fullName profilePhoto username _id",
      })
      .populate({
        path: "uploadedBy",
        select: "agencyName agencyEmail username _id",
      });

    const csvStream = csv.format({ headers: true });
    const exportDir = "public/files/export";

    if (!fs.existsSync(exportDir)) {
      fs.mkdirSync(exportDir, { recursive: true });
    }

    const filePath = `${exportDir}/invoices.csv`;
    const writableStream = fs.createWriteStream(filePath);
    csvStream.pipe(writableStream);

    invoices.forEach((inv) => {
      csvStream.write({
        Company: "Acme Corp",                   // Dummy
        CoFounder: "John Doe",                 // Dummy
        Address: "123 Tech Street, City, IN",  // Dummy
        InvoiceID: inv._id,
        ClientName: inv.client?.fullName || "-",
        ClientUsername: inv.client?.username || "-",
        Campaign: inv.campaign || "-",
        Amount: inv.amount || "-",
        Status: inv.status || "-",
        DueDate: inv.dueDate ? new Date(inv.dueDate).toLocaleDateString() : "-",
        UploadedBy: inv.uploadedBy?.agencyName || "-",
        UploaderEmail: inv.uploadedBy?.agencyEmail || "-",
        CreatedAt: inv.createdAt ? new Date(inv.createdAt).toLocaleDateString() : "-",
        UpdatedAt: inv.updatedAt ? new Date(inv.updatedAt).toLocaleDateString() : "-",
      });
    });

    csvStream.end();

    writableStream.on("finish", () => {
      res.status(200).json({
        success: true,
        downloadUrl: `http://localhost:5000/files/export/invoices.csv`, // hardcoded as you requested
      });
    });
  } catch (err) {
    console.error("CSV Export Error:", err);
    res.status(500).json({
      success: false,
      message: "Failed to export invoices",
    });
  }
};


