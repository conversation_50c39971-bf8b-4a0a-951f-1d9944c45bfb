import { v2 as cloudinary } from 'cloudinary';
import fs from 'fs';
import ModelUser from '../../models/model.js';
import contentModelSchema from '../../models/contentupload/modelcontent.js'
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,  
  api_key:process.env.CLOUDINARY_API_KEY,      
  api_secret:process.env.CLOUDINARY_API_SECRET , 
});

const uploadCounter = {};

export const contentUploadModel = async (req, res) => {
  try {
    const { name, category, note } = req.body;
    const files = req.files;
    if (!name) {
      return res.status(400).json({ success: false, message: 'Username is required' });
    }

    if (!files || files.length === 0) {
      return res.status(400).json({ success: false, message: 'please select a file' });
    }

    // 5 GB limit
    const checkIfUserExists=await ModelUser.findOne({username:name})
    if(!checkIfUserExists){
      return res.status(400).json({ success: false, message: 'user does not exist' });
    }
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);
    const MAX_UPLOAD_SIZE = 5 * 1024 * 1024 * 1024; // 1 GB in bytes

    if (totalSize > MAX_UPLOAD_SIZE) {

      // Clean up uploaded files from tmp
      files.forEach(file => fs.unlinkSync(file.path));
      return res.status(400).json({ success: false, message: 'Upload exceeds 1 GB limit' });
    }

    // Manage folder count per user

    if (!uploadCounter[name]) {
      uploadCounter[name] = 1;
    } else {
      uploadCounter[name]++;
    }

    const cloudFolder = `models/${name}/folder_${uploadCounter[name]}`;
    const uploadedUrls = [];

    for (const file of files) {
      const result = await cloudinary.uploader.upload(file.path, {
        folder: cloudFolder,
        resource_type: 'auto',
      });
      console.log("result",result)
      uploadedUrls.push(result.secure_url);

      fs.unlinkSync(file.path);
    }
      const saveContent=await contentModelSchema.create({
        id:checkIfUserExists._id,
        category,
        name,
        notes:note || "empty",
        urls:uploadedUrls

      })
      console.log(saveContent)
      return res.status(200).json({
      success: true,
      message: 'Files uploaded successfully',
      urls: uploadedUrls,
      folder: cloudFolder,
      metadata: {
        category,
        note,
        totalFiles: files.length,
        totalSizeBytes: totalSize,
      },
    });
   
  } catch (error) {
    console.error("Upload error:", error);
    return res.status(500).json({ success: false, message: 'Server error during upload' });
  }
};
