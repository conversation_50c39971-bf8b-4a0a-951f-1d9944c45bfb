import DmConversation from "../../models/messanger/dmConversations.js";
import DmMessage from "../../models/messanger/dmMessages.js";
import ModelUser from "../../models/model.js";
import Agency from "../../models/agency.js";

export const getAllDMConversations = async (req, res) => {
  try {
    const userId = req.user.id;

    // Step 1: Fetch all conversations the user is part of
    const conversations = await DmConversation.find({
      "members.userId": userId,
    }).lean();

    const convoIds = conversations.map((convo) => convo._id);

    // Step 2: Fetch last 31 messages per conversation (extra 1 to check hasMore)
    const messagesByConvo = {};
    await Promise.all(
      convoIds.map(async (convoId) => {
        const messages = await DmMessage.find({ convoId })
          .sort({ createdAt: -1 }) // newest first
          .limit(31) // fetch 31, show 30
          .lean();

        const hasMoreMessages = messages.length > 30;
        messagesByConvo[convoId.toString()] = {
          messages: messages.slice(0, 30).reverse(), // ✅ reverse to oldest -> newest
          hasMore: hasMoreMessages,
        };
      })
    );

    // Step 3: Collect all member IDs
    const memberIdSet = new Set();
    conversations.forEach((convo) => {
      convo.members.forEach((member) => {
        memberIdSet.add(member.userId.toString());
      });
    });
    const memberIds = Array.from(memberIdSet);

    // Step 4: Fetch user and agency details
    const [modelUsers, agencies] = await Promise.all([
      ModelUser.find({ _id: { $in: memberIds } })
        .select("_id fullName profilePhoto lastOnline")
        .lean(),
      Agency.find({ _id: { $in: memberIds } })
        .select("_id agencyName profilePhoto lastOnline")
        .lean(),
    ]);

    const userDetailsMap = {};

    modelUsers.forEach((user) => {
      userDetailsMap[user._id.toString()] = {
        name: user.fullName,
        avatar: user.profilePhoto || null,
        status: {
          isOnline: null,
          isTyping: null,
          lastOnline: user.lastOnline || null,
        },
      };
    });

    agencies.forEach((agency) => {
      userDetailsMap[agency._id.toString()] = {
        name: agency.agencyName,
        avatar: agency.profilePhoto || null,
        status: {
          isOnline: null,
          isTyping: null,
          lastOnline: agency.lastOnline || null,
        },
      };
    });

    // Step 5: Format final conversations
    const formattedConversations = conversations.map((convo) => {
      const convoIdStr = convo._id.toString();
      const msgData = messagesByConvo[convoIdStr] || {
        messages: [],
        hasMore: false,
      };

      return {
        convoId: convoIdStr,
        type: convo.type,
        createdBy: convo.createdBy,
        createdAt: convo.createdAt,
        isLoading: false,
        hasMore: msgData.hasMore,
        messages: msgData.messages,
        members: convo.members.map((member) => {
          const details = userDetailsMap[member.userId.toString()] || {
            name: null,
            avatar: null,
            status: { isOnline: null, isTyping: null, lastOnline: null },
          };
          return {
            userId: member.userId.toString(),
            joinedAt: member.joinedAt,
            pinned: member.pinned,
            name: details.name,
            avatar: details.avatar,
            status: details.status,
          };
        }),
      };
    });

    return res.status(200).json(formattedConversations);
  } catch (error) {
    console.error("Error fetching DM conversations:", error);
    return res.status(500).json({ error: "Server error" });
  }
};

export const fetchOlderDMMessages = async (req, res) => {
  try {
    const { convoId, oldestTimestamp } = req.query;

    if (!convoId || !oldestTimestamp) {
      return res
        .status(400)
        .json({ error: "Missing convoId or oldestTimestamp" });
    }

    const timestamp = new Date(oldestTimestamp);
    const LIMIT = 30;

    // Fetch 31 messages older than the given timestamp, newest first
    let messages = await DmMessage.find({
      convoId,
      createdAt: { $lt: timestamp },
    })
      .sort({ createdAt: -1 }) // newest of the older ones first
      .limit(LIMIT + 1)
      .lean();

    const hasMore = messages.length > LIMIT;

    // Only keep the first 30 (most recent of the older messages)
    if (hasMore) messages = messages.slice(0, LIMIT);

    // Now reverse so frontend gets oldest → newest
    messages.reverse();

    return res.status(200).json({
      messages,
      hasMore,
    });
  } catch (error) {
    console.error("Error fetching more DM messages:", error);
    return res.status(500).json({ error: "Server error" });
  }
};


//utility function

export const deleteAllDmsOfUser = async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: "userId is required in body" });
    }

    // Step 1: Find all DM conversations where this user is a member
    const conversations = await DmConversation.find({
      "members.userId": userId,
    }).select("_id");

    const convoIds = conversations.map((convo) => convo._id);

    // Step 2: Delete all DM messages from those conversations
    const msgDeleteResult = await DmMessage.deleteMany({
      convoId: { $in: convoIds },
    });

    // Step 3: Delete all those conversations
    const convoDeleteResult = await DmConversation.deleteMany({
      _id: { $in: convoIds },
    });

    return res.status(200).json({
      message: `Deleted ${convoDeleteResult.deletedCount} conversations and ${msgDeleteResult.deletedCount} messages for user ${userId}`,
    });
  } catch (error) {
    console.error("Error deleting DMs for user:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const createNewDm = async (req, res) => {
  try {
    const { targetUserId } = req.body;
    const createdBy = req.user.id;

    // Check for existing DM
    const existingConvo = await DmConversation.findOne({
      "members.userId": { $all: [createdBy, targetUserId] },
      $expr: { $eq: [{ $size: "$members" }, 2] },
    }).lean();

    const convo =
      existingConvo ||
      (await DmConversation.create({
        type: "dm",
        members: [{ userId: createdBy }, { userId: targetUserId }],
        createdBy,
      }));

    // Normalize convo object (whether found or created)
    const convoDoc = existingConvo ? convo : convo.toObject();

    const memberIds = convoDoc.members.map((m) => m.userId.toString());

    // Fetch user details
    const [modelUsers, agencies] = await Promise.all([
      ModelUser.find({ _id: { $in: memberIds } })
        .select("_id fullName profilePhoto lastOnline")
        .lean(),
      Agency.find({ _id: { $in: memberIds } })
        .select("_id agencyName profilePhoto lastOnline")
        .lean(),
    ]);

    const userDetailsMap = {};
    modelUsers.forEach((user) => {
      userDetailsMap[user._id.toString()] = {
        name: user.fullName,
        avatar: user.profilePhoto || null,
        status: {
          isOnline: null,
          isTyping: null,
          lastOnline: user.lastOnline || null,
        },
      };
    });

    agencies.forEach((agency) => {
      userDetailsMap[agency._id.toString()] = {
        name: agency.agencyName,
        avatar: agency.profilePhoto || null,
        status: {
          isOnline: null,
          isTyping: null,
          lastOnline: agency.lastOnline || null,
        },
      };
    });

    const formattedConvo = {
      convoId: convoDoc._id.toString(),
      type: convo.type,
      createdBy: convoDoc.createdBy,
      createdAt: convoDoc.createdAt,
      isLoading: false,
      hasMore: false,
      messages: [], // no messages on creation
      members: convoDoc.members.map((member) => {
        const details = userDetailsMap[member.userId.toString()] || {
          name: null,
          avatar: null,
          status: { isOnline: null, isTyping: null, lastOnline: null },
        };
        return {
          userId: member.userId.toString(),
          joinedAt: member.joinedAt || null,
          pinned: member.pinned || false,
          name: details.name,
          avatar: details.avatar,
          status: details.status,
        };
      }),
    };

    return res.status(existingConvo ? 200 : 201).json({
      success: true,
      conversation: formattedConvo,
    });
  } catch (error) {
    console.error("Error creating new DM:", error);
    return res.status(500).json({ success: false, message: "Server error" });
  }
};
