import ModelAnswer from "../../models/questionnaire/ModelAnswer.js";
import TemplateAssignment from "../../models/questionnaire/TemplateAssignment.js";
import Template from "../../models/questionnaire/Template.js";
import ModelUser from "../../models/model.js";
import PDFGenerator from "../../utils/pdfGeneratorWrapper.js";

export const submitTemplateAnswers = async (req, res) => {
  try {
    const { templateId, answers } = req.body;

    if (req.user.role !== "model") {
      return res.status(403).json({ error: "Only models can submit answers" });
    }

    // Use findOneAndUpdate with upsert to prevent duplicates
    const newAnswers = await ModelAnswer.findOneAndUpdate(
      {
        modelId: req.user.id,
        templateId,
      },
      {
        modelId: req.user.id,
        templateId,
        answers,
        submittedAt: new Date(),
      },
      {
        new: true, // Return the updated document
        upsert: true, // Create if doesn't exist
      }
    );

    await TemplateAssignment.findOneAndUpdate(
      { modelId: req.user.id, templateId },
      { status: "Submitted", submittedAt: new Date() }
    );

    res.status(201).json(newAnswers);
  } catch (error) {
    res
      .status(500)
      .json({ error: error.message || "Failed to submit answers" });
  }
};

export const getModelAnswers = async (req, res) => {
  try {
    if (req.user.role !== "agency") {
      return res
        .status(403)
        .json({ error: "Only agencies can view model answers" });
    }

    const { format = 'json' } = req.query; // Default to JSON if not specified

    // Fetch all required data
    const [modelData, templateData, answersData] = await Promise.all([
      ModelUser.findById(req.params.modelId),
      Template.findById(req.params.templateId),
      ModelAnswer.findOne({
        modelId: req.params.modelId,
        templateId: req.params.templateId,
      }).sort({ submittedAt: -1 }) // Get the latest submission
    ]);

    if (!answersData) {
      return res.status(404).json({ message: "No answers found" });
    }

    // If PDF format is requested
    if (format === 'pdf') {
      if (!modelData || !templateData) {
        return res.status(404).json({ error: 'Required data not found for PDF generation' });
      }

      try {
        // Configure contact information for PDF
        const pdfOptions = {
          email: '<EMAIL>',
          phone: '+****************',
          website: 'www.modelsuite.ai'
        };

        const pdf = await PDFGenerator.generateQuestionnaireReport(
          modelData,
          answersData.answers,
          templateData,
          pdfOptions
        );

        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename=questionnaire-${req.params.modelId}.pdf`);
        return res.send(pdf);
      } catch (pdfError) {
        console.error('PDF Generation Error:', pdfError);
        return res.status(500).json({ error: 'Failed to generate PDF report' });
      }
    }

    // Default JSON response (original functionality preserved)
    res.json(answersData);
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch answers" });
  }
};
// Model retrieves their own answers for a template
export const getMyAnswers = async (req, res) => {
  try {
    if (req.user.role !== "model") {
      return res
        .status(403)
        .json({ error: "Only models can view their answers" });
    }
    const templateId = req.params.templateId;
    const record = await ModelAnswer.findOne({
      modelId: req.user.id,
      templateId,
    });
    const answers = record?.answers || [];
    res.json({ answers });
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch my answers" });
  }
};

// Agency gets all answers for a template (for analytics)
export const getTemplateAnswers = async (req, res) => {
  try {
    if (req.user.role !== "agency") {
      return res
        .status(403)
        .json({ error: "Only agencies can view template analytics" });
    }

    const templateId = req.params.templateId;

    // Get all answers for this template
    const answers = await ModelAnswer.find({ templateId });

    res.json({ answers });
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch template answers" });
  }
};
