import TemplateAssignment from "../../models/questionnaire/TemplateAssignment.js";

export const assignTemplateToModel = async (req, res) => {
  try {
    const { templateId, modelId, modelIds } = req.body;
    if (req.user.role !== "agency") {
      return res
        .status(403)
        .json({ error: "Only agencies can assign templates" });
    }
    // Bulk assignment if modelIds array provided
    if (Array.isArray(modelIds) && modelIds.length > 0) {
      const assignments = await Promise.all(
        modelIds.map((mId) =>
          TemplateAssignment.create({
            templateId,
            modelId: mId,
            agencyId: req.user.id,
          })
        )
      );
      return res.status(201).json(assignments);
    }
    // Single assignment
    if (!modelId) {
      return res.status(400).json({ error: "modelId is required" });
    }
    const assignment = await TemplateAssignment.create({
      templateId,
      modelId,
      agencyId: req.user.id,
    });
    res.status(201).json(assignment);
  } catch (error) {
    res.status(500).json({ message: "Failed to assign template" });
  }
};

export const getAssignedTemplatesForModel = async (req, res) => {
  try {
    if (req.user.role !== "model") {
      return res
        .status(403)
        .json({ error: "Only models can view assigned templates" });
    }

    const assignments = await TemplateAssignment.find({
      modelId: req.user.id,
    }).populate("templateId");

    res.json(assignments);
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch assigned templates" });
  }
};

export const getAssignmentsForAgency = async (req, res) => {
  try {
    if (req.user.role !== "agency") {
      return res
        .status(403)
        .json({ error: "Only agencies can view assigned templates" });
    }

    const assignments = await TemplateAssignment.find({
      agencyId: req.user.id,
    })
      .populate("templateId")
      .populate("modelId", "fullName email");

    res.json(assignments);
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch assignments" });
  }
};

// Update assignment status
export const updateAssignmentStatus = async (req, res) => {
  try {
    const { assignmentId } = req.params;
    const { status } = req.body;

    // Validate status
    const validStatuses = ["Not started", "In progress", "Submitted"];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ error: "Invalid status" });
    }

    // Find and update the assignment
    const assignment = await TemplateAssignment.findById(assignmentId);
    
    if (!assignment) {
      return res.status(404).json({ error: "Assignment not found" });
    }

    // Check if user owns this assignment (model) or created it (agency)
    if (req.user.role === "model" && assignment.modelId.toString() !== req.user.id) {
      return res.status(403).json({ error: "Access denied" });
    }
    if (req.user.role === "agency" && assignment.agencyId.toString() !== req.user.id) {
      return res.status(403).json({ error: "Access denied" });
    }

    // Update status and submitted date if status is "Submitted"
    assignment.status = status;
    if (status === "Submitted") {
      assignment.submittedAt = new Date();
    }

    await assignment.save();

    res.json(assignment);
  } catch (error) {
    console.error("Error updating assignment status:", error);
    res.status(500).json({ message: "Failed to update assignment status" });
  }
};
