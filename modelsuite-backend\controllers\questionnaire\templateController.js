import Template from "../../models/questionnaire/Template.js";

export const createTemplate = async (req, res) => {
  try {
    const { title, description, sections } = req.body;

    if (req.user.role !== "agency") {
      return res
        .status(403)
        .json({ error: "Only agencies can create templates" });
    }

    const newTemplate = await Template.create({
      title,
      description,
      sections,
      agencyId: req.user.id,
      createdBy: req.user.id,
    });

    res.status(201).json(newTemplate);
  } catch (error) {
    res.status(500).json({ message: "Failed to create template" });
  }
};

export const getTemplates = async (req, res) => {
  try {
    if (req.user.role !== "agency") {
      return res
        .status(403)
        .json({ error: "Only agencies can view templates" });
    }

    const templates = await Template.find({
      $or: [{ agencyId: null }, { agencyId: req.user.id }],
    });

    res.json(templates);
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch templates" });
  }
};

export const getTemplateById = async (req, res) => {
  try {
    const template = await Template.findById(req.params.id);

    if (!template) {
      return res.status(404).json({ message: "Template not found" });
    }

    res.json(template);
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch template" });
  }
};

export const updateTemplate = async (req, res) => {
  try {
    if (req.user.role !== "agency") {
      return res
        .status(403)
        .json({ error: "Only agencies can update templates" });
    }

    const { title, description, sections } = req.body;
    const updatedTemplate = await Template.findOneAndUpdate(
      { _id: req.params.id, agencyId: req.user.id },
      { title, description, sections },
      { new: true }
    );

    if (!updatedTemplate) {
      return res
        .status(404)
        .json({ message: "Template not found or not owned" });
    }

    res.json(updatedTemplate);
  } catch (error) {
    res.status(500).json({ message: "Failed to update template" });
  }
};

export const deleteTemplate = async (req, res) => {
  try {
    if (req.user.role !== "agency") {
      return res
        .status(403)
        .json({ error: "Only agencies can delete templates" });
    }

    const deletedTemplate = await Template.findOneAndDelete({
      _id: req.params.id,
      agencyId: req.user.id,
    });

    if (!deletedTemplate) {
      return res
        .status(404)
        .json({ message: "Template not found or not owned" });
    }

    res.json({ message: "Template deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: "Failed to delete template" });
  }
};
