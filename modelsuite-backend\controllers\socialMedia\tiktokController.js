import ModelUser from "../../models/model.js";
import tikapi from "../../utils/tikapiClient.js"; // ✅ using the shared instance

export const handleTikTokCallback = async (req, res) => {
  const { access_token, scope, state } = req.query;

  if (!access_token) {
    return res.status(400).send("Access token missing");
  }

  try {
    const userId = state;
    const model = await ModelUser.findById(userId);
    if (!model) return res.status(404).send("User not found");

    // ✅ Use TikAPI SDK correctly
    const User = new tikapi.user({ accountKey: access_token });
    const response = await User.info();

    const profile = response?.json?.userInfo?.user;

    if (!profile?.uniqueId) {
      return res.status(400).send("Unable to fetch TikTok user profile.");
    }

    // ✅ Save TikTok account data
    model.tiktok = {
      accountKey: access_token,
      scope: scope?.split("+") || [],
      username: profile.uniqueId,
      connectedAt: new Date(),
    };

    await model.save();

    console.log("✅ TikTok connected:", profile.uniqueId);

    return res.redirect(
      `${process.env.FRONTEND_HOSTING_BASEURL}/tiktok/success?modelId=${userId}`
    );
  } catch (err) {
    console.error("❌ TikTok OAuth Callback Error:", err.message);
    return res
      .status(500)
      .send("Something went wrong during TikTok connection.");
  }
};


export const getTikTokProfile = async (req, res) => {
  try {
    const model = await ModelUser.findById(req.user.id);

    if (!model?.tiktok?.accountKey) {
      return res
        .status(400)
        .json({ success: false, message: "TikTok not connected" });
    }

    const User = new tikapi.user({
      accountKey: model.tiktok.accountKey,
    });

    const response = await User.info(); // fetch profile

    return res.status(200).json({ success: true, profile: response.json });
  } catch (err) {
    console.error("TikTok profile fetch error:", err?.message);
    return res.status(500).json({
      success: false,
      message: err?.message || "Failed to fetch profile",
    });
  }
};

export const getTikTokStats = async (req, res) => {
  try {
    const model = await ModelUser.findById(req.user.id);
    if (!model?.tiktok?.accountKey) {
      return res
        .status(400)
        .json({ success: false, message: "TikTok not connected" });
    }

    const userInstance = new tikapi.user({
      accountKey: model.tiktok.accountKey,
    });

    const response = await userInstance.analytics({ type: "overview" });

    return res.status(200).json({
      success: true,
      analytics: response.json,
    });
  } catch (err) {
    console.error(
      "TikTok analytics fetch error:",
      err?.statusCode,
      err?.message,
      err?.json
    );
    return res.status(500).json({
      success: false,
      message:
        err?.json?.message ||
        err?.message ||
        "Failed to fetch TikTok analytics",
    });
  }
};

export const disconnectTikTok = async (req, res) => {
  try {
    const model = await ModelUser.findById(req.user.id);

    if (!model?.tiktok?.accountKey) {
      return res
        .status(400)
        .json({ success: false, message: "TikTok already disconnected" });
    }

    // Instead of setting tiktok to null, just reset its fields
    model.tiktok.accountKey = undefined;
    model.tiktok.scope = [];
    model.tiktok.username = "";
    model.tiktok.connectedAt = undefined;

    await model.save();

    return res
      .status(200)
      .json({ success: true, message: "TikTok disconnected successfully" });
  } catch (err) {
    console.error("TikTok disconnect error:", err.message);
    return res
      .status(500)
      .json({ success: false, message: "Failed to disconnect TikTok" });
  }
};

export const isTikTokConnected = async (req, res) => {
  try {
    const model = await ModelUser.findById(req.user.id);

    if (!model)
      return res
        .status(404)
        .json({ connected: false, message: "User not found" });

    const accountKey = model?.tiktok?.accountKey;
    const isConnected = Boolean(accountKey && accountKey.trim().length > 0);

    console.log("TikTok connection check for user:", model._id);
    console.log("accountKey:", accountKey);

    return res.status(200).json({ connected: isConnected });
  } catch (err) {
    console.error("Error in isTikTokConnected:", err.message);
    return res
      .status(500)
      .json({ message: "Error checking TikTok connection" });
  }
};
