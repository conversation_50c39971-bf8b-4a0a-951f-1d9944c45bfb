import Board from "../../models/task/Board.js";
import TaskList from "../../models/task/List.js";

// Create a new board
export const createBoard = async (req, res) => {
  try {
    const { title, description, isTemplate, background, modelId } = req.body;
    const agencyId = req.user.id;

    if (!modelId) {
      return res.status(400).json({ error: "Model ID is required" });
    }

    const board = await Board.create({
      title,
      description,
      agencyId,
      modelId,
      background: background || "bg-gradient-to-br from-blue-500 to-cyan-500",
      isTemplate: isTemplate || false,
      members: [
        {
          user: agencyId,
          userType: 'Agency',
          role: 'admin'
        },
        {
          user: modelId,
          userType: 'ModelUser',
          role: 'member'
        }
      ]
    });

    // Create default lists
    const defaultLists = ['To Do', 'In Progress', 'On Hold', 'Done'];
    const lists = await Promise.all(
      defaultLists.map((title, index) => 
        TaskList.create({
          title,
          boardId: board._id,
          position: index
        })
      )
    );

    board.lists = lists.map(list => list._id);
    await board.save();

    res.status(201).json(board);
  } catch (err) {
    console.error("Failed to create board:", err);
    res.status(500).json({ error: "Failed to create board" });
  }
};

// Get all boards for an agency or model
export const getAgencyBoards = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const { modelId } = req.query;

    if (!modelId) {
      return res.status(400).json({ error: "Model ID is required" });
    }

    let query = {
      modelId,
      isTemplate: false,
      isArchived: false
    };

    // If user is an agency, add agencyId to query
    if (userRole === 'agency') {
      query.agencyId = userId;
    } else if (userRole === 'model') {
      // If user is a model, ensure they can only see their own boards
      if (userId !== modelId) {
        return res.status(403).json({ error: "Not authorized to view these boards" });
      }
    }

    const boards = await Board.find(query)
      .populate({
        path: 'lists',
        select: 'title cards position isArchived',
        match: { isArchived: false },
        options: { sort: { position: 1 } }
      })
      .sort('-updatedAt');

    res.json(boards);
  } catch (err) {
    console.error("Failed to get boards:", err);
    res.status(500).json({ error: "Failed to get boards" });
  }
};

// Get a single board by ID
export const getBoardById = async (req, res) => {
  try {
    const { boardId } = req.params;
    const userRole = req.user.role;
    
    // First, get the board with basic info
    const board = await Board.findById(boardId)
      .populate({
        path: 'lists',
        match: { isArchived: false },
        options: { sort: { position: 1 } }
      });

    if (!board) {
      return res.status(404).json({ error: "Board not found" });
    }

    // Check if user has access
    const isMember = board.members.some(m => 
      m.user.toString() === req.user.id
    );

    if (!isMember) {
      return res.status(403).json({ error: "Not authorized to view this board" });
    }

    // Now populate the lists with their cards
    await Board.populate(board, {
      path: 'lists',
      populate: {
        path: 'cards',
        model: 'Card',
        match: { isArchived: false },
        options: { sort: { position: 1 } },
        select: 'title description position isComplete dueDate assignedTo labels priority isOnHold onHoldReason onHoldBy',
        populate: [
          {
            path: 'assignedTo',
            model: 'ModelUser',
            select: 'fullName profilePhoto'
          },
          {
            path: 'onHoldBy.user',
            model: userRole === 'agency' ? 'Agency' : 'ModelUser',
            select: 'fullName profilePhoto agencyName'
          }
        ]
      }
    });

    // If user is a model, remove certain fields they shouldn't see
    if (userRole === 'model') {
      board.members = undefined;
    }

    res.json(board);
  } catch (err) {
    console.error("Failed to get board:", err);
    res.status(500).json({ error: "Failed to get board" });
  }
};

// Update a board
export const updateBoard = async (req, res) => {
  try {
    const { boardId } = req.params;
    const updates = req.body;
    
    const board = await Board.findById(boardId);
    if (!board) {
      return res.status(404).json({ error: "Board not found" });
    }

    // Check if user has admin permission
    const member = board.members.find(m => 
      m.user.toString() === req.user.id && m.role === 'admin'
    );
    if (!member) {
      return res.status(403).json({ error: "Not authorized to update board" });
    }

    // Only allow updating certain fields
    const allowedUpdates = [
      'title',
      'description',
      'background',
      'isArchived',
      'starred'
    ];

    const filteredUpdates = Object.keys(updates)
      .filter(key => allowedUpdates.includes(key))
      .reduce((obj, key) => {
        obj[key] = updates[key];
        return obj;
      }, {});

    Object.assign(board, filteredUpdates);
    await board.save();

    res.json(board);
  } catch (err) {
    console.error("Failed to update board:", err);
    res.status(500).json({ error: "Failed to update board" });
  }
};

// Delete a board
export const deleteBoard = async (req, res) => {
  try {
    const { boardId } = req.params;
    
    const board = await Board.findById(boardId);
    if (!board) {
      return res.status(404).json({ error: "Board not found" });
    }

    // Check if user has admin permission
    const member = board.members.find(m => 
      m.user.toString() === req.user.id && m.role === 'admin'
    );
    if (!member) {
      return res.status(403).json({ error: "Not authorized to delete board" });
    }

    // Delete all associated lists and cards
    await TaskList.deleteMany({ boardId: boardId });
    await board.deleteOne();

    res.json({ message: "Board deleted successfully" });
  } catch (err) {
    console.error("Failed to delete board:", err);
    res.status(500).json({ error: "Failed to delete board" });
  }
}; 