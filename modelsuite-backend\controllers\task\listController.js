import TaskList from "../../models/task/List.js";
import Board from "../../models/task/Board.js";
import TaskCard from "../../models/task/Card.js";

// Create a new list
export const createList = async (req, res) => {
  try {
    const { boardId } = req.params;
    const { title } = req.body;

    // Check board exists and user has access
    const board = await Board.findById(boardId);
    if (!board) {
      return res.status(404).json({ error: "Board not found" });
    }

    // Get highest position
    const lastList = await TaskList.findOne({ boardId: boardId })
      .sort('-position');
    const position = lastList ? lastList.position + 1 : 0;

    const list = await TaskList.create({
      title,
      boardId: boardId,
      position
    });

    // Add list to board
    board.lists.push(list._id);
    await board.save();

    res.status(201).json(list);
  } catch (err) {
    console.error("Failed to create list:", err);
    res.status(500).json({ error: "Failed to create list" });
  }
};

// Update a list
export const updateList = async (req, res) => {
  try {
    const { listId } = req.params;
    const updates = req.body;

    const list = await TaskList.findById(listId);
    if (!list) {
      return res.status(404).json({ error: "List not found" });
    }

    // Check board access
    const board = await Board.findById(list.boardId);
    if (!board) {
      return res.status(404).json({ error: "Board not found" });
    }

    Object.assign(list, updates);
    await list.save();

    res.json(list);
  } catch (err) {
    console.error("Failed to update list:", err);
    res.status(500).json({ error: "Failed to update list" });
  }
};

// Delete a list
export const deleteList = async (req, res) => {
  try {
    const { listId } = req.params;

    const list = await TaskList.findById(listId);
    if (!list) {
      return res.status(404).json({ error: "List not found" });
    }

    // Check board access and remove list reference
    const board = await Board.findById(list.boardId);
    if (!board) {
      return res.status(404).json({ error: "Board not found" });
    }

    board.lists = board.lists.filter(id => id.toString() !== listId);
    await board.save();

    // Delete all cards in the list
    await TaskCard.deleteMany({ listId: listId });
    await list.deleteOne();

    res.json({ message: "List deleted successfully" });
  } catch (err) {
    console.error("Failed to delete list:", err);
    res.status(500).json({ error: "Failed to delete list" });
  }
};

// Reorder lists in a board
export const reorderLists = async (req, res) => {
  try {
    const { boardId } = req.params;
    const { listIds } = req.body; // Array of list IDs in new order

    // Update position for each list
    await Promise.all(
      listIds.map(async (listId, index) => {
        await TaskList.findByIdAndUpdate(listId, { position: index });
      })
    );

    const updatedLists = await TaskList.find({ boardId: boardId })
      .sort('position');

    res.json(updatedLists);
  } catch (err) {
    console.error("Failed to reorder lists:", err);
    res.status(500).json({ error: "Failed to reorder lists" });
  }
}; 