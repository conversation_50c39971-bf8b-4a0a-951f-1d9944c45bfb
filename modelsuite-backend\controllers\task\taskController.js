import Task from "../../models/task/Task.js";
import cloudinary from "../../config/cloudinary.js";

// Get tasks for a model
export const getTasks = async (req, res) => {
  try {
    const { modelId } = req.query;
    const userRole = req.user.role;

    if (!modelId) {
      return res.status(400).json({ error: "Model ID is required" });
    }

    // If user is a model, ensure they can only see their own tasks
    if (userRole === 'model' && modelId !== req.user.id) {
      return res.status(403).json({ error: "Not authorized to view these tasks" });
    }

    const tasks = await Task.find({
      modelId,
      isArchived: false
    })
    .populate('assignedTo', 'fullName profilePhoto')
    .populate({
      path: 'onHoldBy.userId',
      select: 'fullName profilePhoto agencyName'
    })
    .populate({
      path: 'createdBy.userId',
      select: 'fullName profilePhoto agencyName'
    })
    .sort('-createdAt');

    res.json(tasks);
  } catch (err) {
    console.error("Failed to get tasks:", err);
    res.status(500).json({ error: "Failed to get tasks" });
  }
};

// Create a new task
export const createTask = async (req, res) => {
  try {
    const { title, description, dueDate, members, labels, priority, modelId } = req.body;
    const userRole = req.user.role;
    const userId = req.user.id;

    if (!modelId) {
      return res.status(400).json({ error: "Model ID is required" });
    }

    // Validate priority if provided
    const validPriorities = ['critical', 'high', 'medium', 'low'];
    if (priority && !validPriorities.includes(priority)) {
      return res.status(400).json({ error: "Invalid priority value. Must be one of: critical, high, medium, low" });
    }

    const task = await Task.create({
      title,
      description,
      dueDate,
      assignedTo: members,
      labels,
      priority: priority || 'medium',
      modelId,
      agencyId: userRole === 'agency' ? userId : null,
      createdBy: {
        userId: userId,
        userType: userRole === 'model' ? 'ModelUser' : 'Agency'
      }
    });

    const populatedTask = await Task.findById(task._id)
      .populate('assignedTo', 'fullName profilePhoto')
      .populate({
        path: 'createdBy.userId',
        select: 'fullName profilePhoto agencyName'
      });

    res.status(201).json(populatedTask);
  } catch (err) {
    console.error("Failed to create task:", err);
    res.status(500).json({ error: "Failed to create task" });
  }
};

// Update a task
export const updateTask = async (req, res) => {
  try {
    const { taskId } = req.params;
    const updates = req.body;

    // Validate priority if provided
    if (updates.priority) {
      const validPriorities = ['critical', 'high', 'medium', 'low'];
      if (!validPriorities.includes(updates.priority)) {
        return res.status(400).json({ error: "Invalid priority value. Must be one of: critical, high, medium, low" });
      }
    }

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    Object.assign(task, updates);
    await task.save();

    const updatedTask = await Task.findById(taskId)
      .populate('assignedTo', 'fullName profilePhoto')
      .populate({
        path: 'onHoldBy.userId',
        select: 'fullName profilePhoto agencyName'
      })
      .populate({
        path: 'createdBy.userId',
        select: 'fullName profilePhoto agencyName'
      });

    res.json(updatedTask);
  } catch (err) {
    console.error("Failed to update task:", err);
    res.status(500).json({ error: "Failed to update task" });
  }
};

// Delete a task
export const deleteTask = async (req, res) => {
  try {
    const { taskId } = req.params;
    const userRole = req.user.role;

    // Only agency can delete tasks
    if (userRole !== 'agency') {
      return res.status(403).json({ error: "Only agency can delete tasks" });
    }

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    // Delete all attachments from cloudinary
    for (const attachment of task.attachments) {
      if (attachment.publicId) {
        await cloudinary.uploader.destroy(attachment.publicId);
      }
    }

    await task.deleteOne();
    res.json({ message: "Task deleted successfully" });
  } catch (err) {
    console.error("Failed to delete task:", err);
    res.status(500).json({ error: "Failed to delete task" });
  }
};

// Put task on hold
export const putTaskOnHold = async (req, res) => {
  try {
    const { taskId } = req.params;
    const { reason } = req.body;
    const userRole = req.user.role;
    const userId = req.user.id;

    if (!reason || !reason.trim()) {
      return res.status(400).json({ error: "Hold reason is required" });
    }

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    task.isOnHold = true;
    task.onHoldReason = reason.trim();
    task.onHoldBy = {
      userId: userId,
      userType: userRole === 'model' ? 'ModelUser' : 'Agency',
      timestamp: new Date()
    };

    await task.save();

    const updatedTask = await Task.findById(taskId)
      .populate('assignedTo', 'fullName profilePhoto')
      .populate({
        path: 'onHoldBy.userId',
        select: 'fullName profilePhoto agencyName'
      })
      .populate({
        path: 'createdBy.userId',
        select: 'fullName profilePhoto agencyName'
      });

    res.json(updatedTask);
  } catch (err) {
    console.error("Failed to put task on hold:", err);
    res.status(500).json({ error: "Failed to put task on hold" });
  }
};

// Add a comment to a task
export const addComment = async (req, res) => {
  try {
    const { taskId } = req.params;
    const { text } = req.body;
    const userId = req.user.id;
    const userRole = req.user.role;

    if (!text || !text.trim()) {
      return res.status(400).json({ error: "Comment text is required" });
    }

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    task.comments.push({
      text: text.trim(),
      author: userId,
      authorType: userRole === 'model' ? 'ModelUser' : 'Agency'
    });

    await task.save();

    const updatedTask = await Task.findById(taskId)
      .populate('assignedTo', 'fullName profilePhoto')
      .populate({
        path: 'comments.author',
        select: 'fullName profilePhoto agencyName'
      })
      .populate({
        path: 'onHoldBy.userId',
        select: 'fullName profilePhoto agencyName'
      })
      .populate({
        path: 'createdBy.userId',
        select: 'fullName profilePhoto agencyName'
      });

    res.json(updatedTask);
  } catch (err) {
    console.error("Failed to add comment:", err);
    res.status(500).json({ error: "Failed to add comment" });
  }
};

// Get comments for a task
export const getComments = async (req, res) => {
  try {
    const { taskId } = req.params;

    const task = await Task.findById(taskId)
      .populate({
        path: 'comments.author',
        select: 'fullName profilePhoto agencyName'
      })
      .select('comments');

    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    res.json(task.comments);
  } catch (err) {
    console.error("Failed to get comments:", err);
    res.status(500).json({ error: "Failed to get comments" });
  }
};

// Delete a comment
export const deleteComment = async (req, res) => {
  try {
    const { taskId, commentId } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    const comment = task.comments.id(commentId);
    if (!comment) {
      return res.status(404).json({ error: "Comment not found" });
    }

    // Check if user is authorized to delete the comment
    if (comment.author.toString() !== userId && userRole !== 'agency') {
      return res.status(403).json({ error: "Not authorized to delete this comment" });
    }

    comment.deleteOne();
    await task.save();

    const updatedTask = await Task.findById(taskId)
      .populate('assignedTo', 'fullName profilePhoto')
      .populate({
        path: 'comments.author',
        select: 'fullName profilePhoto agencyName'
      })
      .populate({
        path: 'onHoldBy.userId',
        select: 'fullName profilePhoto agencyName'
      })
      .populate({
        path: 'createdBy.userId',
        select: 'fullName profilePhoto agencyName'
      });

    res.json(updatedTask);
  } catch (err) {
    console.error("Failed to delete comment:", err);
    res.status(500).json({ error: "Failed to delete comment" });
  }
};

// Get task attachments
export const getAttachments = async (req, res) => {
  try {
    const { taskId } = req.params;

    const task = await Task.findById(taskId).select('attachments');
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    res.json(task.attachments);
  } catch (err) {
    console.error("Failed to get attachments:", err);
    res.status(500).json({ error: "Failed to get attachments" });
  }
};

// Upload attachment
export const uploadAttachment = async (req, res) => {
  try {
    const { taskId } = req.params;
    const file = req.file;
    const userId = req.user.id;
    const userRole = req.user.role;

    if (!file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    // Upload to cloudinary
    const result = await cloudinary.uploader.upload(
      "data:" + file.mimetype + ";base64," + file.buffer.toString('base64'),
      {
        resource_type: "auto",
        folder: "task-attachments"
      }
    );

    // Add attachment to task
    task.attachments.push({
      url: result.secure_url,
      publicId: result.public_id,
      type: file.mimetype,
      originalName: file.originalname,
      uploadedBy: userId,
      uploadedByType: userRole === 'model' ? 'ModelUser' : 'Agency'
    });

    await task.save();

    res.json(task.attachments[task.attachments.length - 1]);
  } catch (err) {
    console.error("Failed to upload attachment:", err);
    res.status(500).json({ error: "Failed to upload attachment" });
  }
};

// Delete attachment
export const deleteAttachment = async (req, res) => {
  try {
    const { taskId, attachmentId } = req.params;
    const userRole = req.user.role;

    // Only agency can delete attachments
    if (userRole !== 'agency') {
      return res.status(403).json({ error: "Only agency can delete attachments" });
    }

    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ error: "Task not found" });
    }

    const attachment = task.attachments.id(attachmentId);
    if (!attachment) {
      return res.status(404).json({ error: "Attachment not found" });
    }

    // Delete from cloudinary if publicId exists
    if (attachment.publicId) {
      await cloudinary.uploader.destroy(attachment.publicId);
    }

    // Remove attachment from task
    attachment.deleteOne();
    await task.save();

    res.json({ message: "Attachment deleted successfully" });
  } catch (err) {
    console.error("Failed to delete attachment:", err);
    res.status(500).json({ error: "Failed to delete attachment" });
  }
}; 