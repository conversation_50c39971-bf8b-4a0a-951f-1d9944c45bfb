import UserProfile from '../models/UserProfile.js';
import ModelUser from '../models/model.js';
import upload, { uploadPortfolio } from '../middlewares/multer.js';

// Create a user profile (usually after registration)
export const createUserProfile = async (req, res) => {
  try {
    const { display_name, phone, country, bio, avatar_url, availability, social_links, availability_text, reachability, portfolio } = req.body;
    const userId = req.user.id;

    // Prevent duplicate profile
    const existing = await UserProfile.findOne({ user: userId });
    if (existing) return res.status(409).json({ error: 'Profile already exists' });

    // Get user info for email/role
    const user = await ModelUser.findById(userId);
    if (!user) return res.status(404).json({ error: 'User not found' });

    const profile = new UserProfile({
      user: userId,
      display_name,
      role: user.role,
      email: user.email,
      phone,
      country,
      bio,
      avatar_url,
      availability,
      availability_text,
      reachability,
      social_links,
      portfolio,
    });
    await profile.save();
    res.status(201).json(profile);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
};

// Get current user's profile
export const getMyProfile = async (req, res) => {
  try {
    const userId = req.user.id;
    const profile = await UserProfile.findOne({ user: userId });
    if (!profile) return res.status(404).json({ error: 'Profile not found' });
    res.status(200).json(profile);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
};

// Update current user's profile
export const updateMyProfile = async (req, res) => {
  try {
    const userId = req.user.id;
    const updates = req.body;
    const profile = await UserProfile.findOneAndUpdate(
      { user: userId },
      { $set: updates },
      { new: true, runValidators: true }
    );
    if (!profile) return res.status(404).json({ error: 'Profile not found' });
    res.status(200).json(profile);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
};

// Avatar upload controller
export const uploadAvatar = async (req, res) => {
  try {
    // Multer places the file info in req.file
    if (!req.file || !req.file.path) {
      return res.status(400).json({ error: 'No file uploaded' });
    }
    const userId = req.user.id;
    // Update the UserProfile with the new avatar URL
    const profile = await UserProfile.findOneAndUpdate(
      { user: userId },
      { $set: { avatar_url: req.file.path } },
      { new: true, runValidators: true }
    );
    if (!profile) return res.status(404).json({ error: 'Profile not found' });
    res.status(200).json({ avatar_url: profile.avatar_url, profile });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
};

// Cover photo upload controller
export const uploadCoverPhoto = async (req, res) => {
  try {
    if (!req.file || !req.file.path) {
      return res.status(400).json({ error: 'No file uploaded' });
    }
    const userId = req.user.id;
    const profile = await UserProfile.findOneAndUpdate(
      { user: userId },
      { $set: { cover_url: req.file.path } },
      { new: true, runValidators: true }
    );
    if (!profile) return res.status(404).json({ error: 'Profile not found' });
    res.status(200).json({ cover_url: profile.cover_url, profile });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
};

// Add this controller for portfolio image upload
export const uploadPortfolioImage = [
  uploadPortfolio.single('image'),
  (req, res) => {
    if (!req.file || !req.file.path) {
      return res.status(400).json({ error: 'No file uploaded or invalid file type/size.' });
    }
    res.status(200).json({ url: req.file.path });
  }
];

// Get any user's profile by userId
export const getUserProfileById = async (req, res) => {
  try {
    const { userId } = req.params;
    const profile = await UserProfile.findOne({ user: userId });
    if (!profile) return res.status(404).json({ error: 'Profile not found' });
    res.status(200).json(profile);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
};

// Update any user's profile by userId (admin/agency only)
export const updateUserProfileById = async (req, res) => {
  try {
    const { userId } = req.params;
    const updates = req.body;
    const profile = await UserProfile.findOneAndUpdate(
      { user: userId },
      { $set: updates },
      { new: true, runValidators: true }
    );
    if (!profile) return res.status(404).json({ error: 'Profile not found' });
    res.status(200).json(profile);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Server error' });
  }
}; 