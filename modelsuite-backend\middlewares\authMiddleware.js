import jwt from "jsonwebtoken";
import ModelUser from "../models/model.js";
import Agency from "../models/agency.js";

// export const verifyToken = (req, res, next) => {
//   const authHeader = req.headers.authorization;

//   // Expecting format: "Bearer <token>"
//   const token = authHeader?.split(' ')[1];

//   if (!token) {
//     return res.status(401).json({ error: 'Access denied. No token provided.' });
//   }

//   try {
//     const decoded = jwt.verify(token, process.env.JWT_SECRET);
//     req.user = decoded; // { id, role }
//     next();
//   } catch (err) {
//     console.error(err);
//     return res.status(401).json({ error: 'Invalid or expired token' });
//   }
// };

export const verifyToken = async (req, res, next) => {
  try {
    const token =
      req.cookies?.accessToken ||
      req.header("Authorization")?.replace("Bearer ", "");

    if (!token) return res.status(401).json({ message: "Unauthorized User." });
    let decodedToken;

    try {
      decodedToken = jwt.verify(token, process.env.LOGIN_ACCESS_TOKEN_SECRET);
    } catch (error) {
      if (error.name === "TokenExpiredError") {
        return res
          .status(401)
          .json({ message: "Token expired. Please log in again." });
      }
      return res.status(401).json({ message: "Invalid token." });
    }

    let user = await ModelUser.findById(decodedToken._id).select(
      "-password -loginRefreshToken"
    );

    if (!user) {
      user = await Agency.findById(decodedToken._id).select(
        "-password -loginRefreshToken"
      );
    }

    if (!user) {
      return res.status(401).json({ message: "Invalid Access Token." });
    }

    req.user = user;
    next();
  } catch (error) {
    return res.status(401).json({ message: "Unauthorized User." });
  }
};

export const verifyRole = (role) => {
  return (req, res, next) => {
    if (req.user?.role !== role) {
      return res.status(403).json({ error: "Forbidden: Access denied" });
    }
    next();
  };
};
