// import rateLimit from "express-rate-limit";
// // import { getRealIP } from "../../utils/ipUtils.js";
// // import { getRealIP } from "../../utils/ipUtils.js";
// export const authLimiter = rateLimit({
//   windowMs: 15 * 60 * 1000, // 15 minutes
//   max: 5, // 5 login attempts per 15 minutes
//   // keyGenerator: (req) => getRealIP(req),
//   standardHeaders: true,
//   legacyHeaders: false,
//   message: {
//     error: "Too many login attempts. Please try again in 15 minutes.",
//   },
//   handler: (req, res, next, options) => {
//     // const realIP = getRealIP(req);
//     // console.log(`[⚠️ AUTH RATE LIMITED] Real IP ${realIP} exceeded login limit`);
//     // console.log(`[DEBUG] req.ip: ${req.ip}, headers:`, req.headers);
//     res.status(options.statusCode).json(options.message);
//   },
// });