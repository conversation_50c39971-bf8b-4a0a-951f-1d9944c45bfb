import rateLimit from "express-rate-limit";
export const otpLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 3, // 3 OTP requests per 5 minutes
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    error: "Too many OTP requests. Please try again in 5 minutes.",
  },
  handler: (req, res, next, options) => {
    console.log(`[⚠️ OTP RATE LIMITED] IP ${req.ip} exceeded OTP limit`);
    res.status(options.statusCode).json(options.message);
  },
});