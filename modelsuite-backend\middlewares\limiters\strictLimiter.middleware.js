import rateLimit from "express-rate-limit";

export const strictLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 attempts per hour
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    error: "Too many attempts. Please try again in 1 hour.",
  },
  handler: (req, res, next, options) => {
    console.log(`[⚠️ STRICT RATE LIMITED] IP ${req.ip} exceeded strict limit`);
    res.status(options.statusCode).json(options.message);
  },
});