import Agency from "../models/agency.js";

/**
 * Middleware to allow only users with role 'agency'
 */
export const checkAgency = (req, res, next) => {
  if (req.user && req.user.role === "agency") {
    next();
  } else {
    return res
      .status(403)
      .json({ message: "Access denied: Agency role required" });
  }
};

/**
 * Middleware to allow only users with role 'admin'
 */
export const checkAdmin = (req, res, next) => {
  if (req.user && req.user.role === "admin") {
    next();
  } else {
    return res
      .status(403)
      .json({ message: "Access denied: Admin role required" });
  }
};

/**
 * Middleware to allow only users with role 'model'
 */
export const checkModel = (req, res, next) => {
  if (req.user && req.user.role === "model") {
    next();
  } else {
    return res
      .status(403)
      .json({ message: "Access denied: Model role required" });
  }
};

export const validateModelGroupOperation = async (req, res, next) => {
  try {
    const { modelId, groupId } = req.body;
    
    if (!modelId || !groupId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Model ID and Group ID are required' 
      });
    }

    const agency = await Agency.findById(req.user._id);
    if (!agency) {
      return res.status(404).json({ 
        success: false, 
        message: 'Agency not found' 
      });
    }

    // Validate group exists (except for ungrouped)
    if (groupId !== 'ungrouped') {
      const groupExists = agency.modelGroups.some(group => group.id === groupId);
      if (!groupExists) {
        return res.status(400).json({ 
          success: false, 
          message: 'Invalid group ID' 
        });
      }
    }

    // Add agency to request for controller use
    req.agency = agency;
    next();
  } catch (error) {
    console.error('Validation error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
};

export const requireAdminOrAgency = (req, res, next) => {
  if (req.user && (req.user.role === 'admin' || req.user.role === 'agency')) {
    return next();
  }
  return res.status(403).json({ error: 'Forbidden: Admin or agency only' });
};
