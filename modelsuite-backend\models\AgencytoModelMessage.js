import mongoose from "mongoose";

const agencyToModelMessageSchema = new mongoose.Schema({
  senderId: {
    type: mongoose.Schema.Types.ObjectId,
    required: [true, "Sender Id is required!"],
    refPath: "senderModel",
  },
  senderModel: {
    type: String,
    enum: ["Agency", "ModelUser"], // match exact model names
    required: [true, "Sender model is required!"],
  },
  receiverId: {
    type: mongoose.Schema.Types.ObjectId,
    required: [true, "Receiver Id is required!"],
    refPath: "receiverModel",
  },
  receiverModel: {
    type: String,
    enum: ["Agency", "ModelUser"],
    required: [true, "Receiver Model is required"],
  },
  text: {
    type: String,
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

export default mongoose.model(
  "AgencyToModelMessage",
  agencyToModelMessageSchema
);
