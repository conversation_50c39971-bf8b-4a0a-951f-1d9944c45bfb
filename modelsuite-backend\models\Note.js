import mongoose from "mongoose";

// Schema for note attachments
const attachmentSchema = new mongoose.Schema({
  url: { type: String, required: true },
  publicId: { type: String },
  type: { type: String, required: true }, // 'image', 'document', 'video', etc.
  originalName: { type: String },
  size: { type: Number },
  uploadedAt: { type: Date, default: Date.now }
});

// Schema for note reminders
const reminderSchema = new mongoose.Schema({
  reminderDate: { type: Date, required: true },
  isCompleted: { type: Boolean, default: false },
  completedAt: { type: Date },
  notificationSent: { type: Boolean, default: false }
});

// Schema for linked actions
const linkedActionSchema = new mongoose.Schema({
  actionType: {
    type: String,
    enum: ['task', 'contract', 'meeting', 'follow_up', 'custom'],
    required: true
  },
  actionId: { type: mongoose.Schema.Types.ObjectId },
  actionTitle: { type: String, required: true },
  actionDescription: { type: String },
  dueDate: { type: Date },
  isCompleted: { type: Boolean, default: false },
  completedAt: { type: Date }
});

// Main Note Schema
const noteSchema = new mongoose.Schema({
  // Basic Information
  title: {
    type: String,
    required: [true, "Note title is required"],
    trim: true,
    maxlength: [200, "Title cannot exceed 200 characters"]
  },
  content: {
    type: String,
    required: [true, "Note content is required"]
  },
  
  // Rich Text Support
  contentType: {
    type: String,
    enum: ['plain', 'markdown', 'html'],
    default: 'plain'
  },
  
  // Model Association (Required)
  modelId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "ModelUser",
    required: [true, "Model ID is required"]
  },
  
  // Agency Association (Required)
  agencyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Agency",
    required: [true, "Agency ID is required"]
  },
  
  // Author Information
  createdBy: {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      refPath: 'createdBy.userType',
      required: true
    },
    userType: {
      type: String,
      enum: ['ModelUser', 'Agency'],
      required: true
    }
  },
  
  // Access Control
  visibility: {
    type: String,
    enum: ['internal', 'shared_with_model', 'neutral'],
    default: 'internal'
  },
  
  // Categorization
  category: {
    type: String,
    enum: [
      'general',
      'performance',
      'behavior',
      'communication',
      'contract',
      'payment',
      'content',
      'meeting',
      'follow_up',
      'issue',
      'achievement',
      'custom'
    ],
    default: 'general'
  },
  
  // Custom category for flexibility
  customCategory: {
    type: String,
    trim: true,
    maxlength: [50, "Custom category cannot exceed 50 characters"]
  },
  
  // Priority and Status
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },
  
  status: {
    type: String,
    enum: ['active', 'archived', 'deleted'],
    default: 'active'
  },
  
  // Tags for better organization
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  
  // Pinning functionality
  isPinned: {
    type: Boolean,
    default: false
  },
  
  pinnedAt: {
    type: Date
  },
  
  pinnedBy: {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      refPath: 'pinnedBy.userType'
    },
    userType: {
      type: String,
      enum: ['ModelUser', 'Agency']
    }
  },
  
  // Attachments
  attachments: [attachmentSchema],
  
  // Reminders
  reminders: [reminderSchema],
  
  // Linked Actions
  linkedActions: [linkedActionSchema],
  
  // Audit Trail
  lastEditedBy: {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      refPath: 'lastEditedBy.userType'
    },
    userType: {
      type: String,
      enum: ['ModelUser', 'Agency']
    }
  },
  
  lastEditedAt: {
    type: Date
  },
  
  // Version tracking for audit
  version: {
    type: Number,
    default: 1
  },
  
  // Soft delete
  deletedAt: {
    type: Date
  },
  
  deletedBy: {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      refPath: 'deletedBy.userType'
    },
    userType: {
      type: String,
      enum: ['ModelUser', 'Agency']
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
noteSchema.index({ modelId: 1, agencyId: 1 });
noteSchema.index({ 'createdBy.userId': 1 });
noteSchema.index({ category: 1 });
noteSchema.index({ tags: 1 });
noteSchema.index({ status: 1 });
noteSchema.index({ createdAt: -1 });
noteSchema.index({ isPinned: -1, createdAt: -1 });

// Virtual for checking if note is deleted
noteSchema.virtual('isDeleted').get(function() {
  return !!this.deletedAt;
});

// Pre-save middleware to update version and lastEdited info
noteSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.version += 1;
    this.lastEditedAt = new Date();
  }
  
  // Auto-set pinnedAt when isPinned is set to true
  if (this.isModified('isPinned') && this.isPinned && !this.pinnedAt) {
    this.pinnedAt = new Date();
  }
  
  next();
});

// Static method to find active notes
noteSchema.statics.findActive = function(filter = {}) {
  return this.find({ ...filter, status: 'active', deletedAt: { $exists: false } });
};

// Static method to find notes by model
noteSchema.statics.findByModel = function(modelId, agencyId) {
  return this.findActive({ modelId, agencyId });
};

// Instance method to soft delete
noteSchema.methods.softDelete = function(deletedBy) {
  this.status = 'deleted';
  this.deletedAt = new Date();
  this.deletedBy = deletedBy;
  return this.save();
};

// Instance method to pin/unpin note
noteSchema.methods.togglePin = function(pinnedBy) {
  this.isPinned = !this.isPinned;
  if (this.isPinned) {
    this.pinnedAt = new Date();
    this.pinnedBy = pinnedBy;
  } else {
    this.pinnedAt = undefined;
    this.pinnedBy = undefined;
  }
  return this.save();
};

const Note = mongoose.model("Note", noteSchema);
export default Note;