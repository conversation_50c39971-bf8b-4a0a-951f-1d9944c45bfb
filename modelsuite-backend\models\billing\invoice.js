import mongoose from "mongoose";

const invoiceSchema = new mongoose.Schema(
  {
    client: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: true,
    },
    campaign: { type: String },
    amount: { type: Number, required: true },
    currency: { type: String, required: true },
    dueDate: { type: Date, required: true },
    status: {
      type: String,
      enum: ["Paid", "Unpaid", "Overdue"],
      required: true,
    },
    note: { type: String },
    fileUrl: { type: String, required: true },
    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
    },
  },
  { timestamps: true } // ✅ createdAt will now be used as invoiceDate
);

invoiceSchema.set("toJSON", {
  transform(doc, ret) {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // remove time part for comparison

    if (
      ret.status === "Unpaid" &&
      new Date(ret.dueDate).getTime() < today.getTime()
    ) {
      ret.status = "Overdue"; // ⚠️ only in output (not DB)
    }

    return ret;
  },
});
// ✅ Self-deleting logic: IIFE — runs immediately when file is imported

const Invoice = mongoose.model("Invoice", invoiceSchema);

export default Invoice;


// (async () => {
//   try {
//     const deleted = await Invoice.deleteMany({});
//     console.log(`[Invoice Cleanup] Deleted ${deleted.deletedCount} invoices.`);
//   } catch (error) {
//     console.error("[Invoice Cleanup Error]", error);
//   }
// })();
