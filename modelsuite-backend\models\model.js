import mongoose from "mongoose";
import jwt from "jsonwebtoken";

const modelSchema = new mongoose.Schema(
  {
    role: {
      type: String,
      default: "model",
      immutable: true,
    },
    fullName: {
      type: String,
      required: [true, "Full Name is required!!"],
    },
    profilePhoto: String,
    dob: {
      type: Date,
    },
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      default: null,
    },
    country: String,
    city: String,
    category: [String],

    email: {
      type: String,
      // required: true
      unique: true,
    },
    phone: {
      type: String,
      unique: true,
    },
    username: {
      type: String,
      required: [true, "Username is required!"],
      unique: true,
    },
    password: {
      type: String,
      required: [true, "Password is required!"],
    },
    googleAccessToken: {
      type: String,
    },

    googleRefreshToken: {
      type: String,
    },

    tokenExpiryDate: {
      type: Date,
    },
    assignedTasks: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Task",
      },
    ],
    instagramAccount: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "InstagramAccount",
      unique: true,
    },
    comments: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "TaskComment",
      },
    ],
    groups: [{ type: mongoose.Schema.Types.ObjectId, ref: "Group" }],
    lastOnline: {
      type: String,
      default: null,
    },
    isMfaActive: {
      type: Boolean,
      default: false,
    },
    phoneVerified: {
      type: Boolean,
      default: false,
    },
    emailVerified: {
      type: Boolean,
      default: false,
    },
    isAcceptingEmail: {
      type: Boolean,
      default: false,
    },
    loginRefreshToken: {
      type: String,
    },
    magicLinkToken: {
      type: String,
    },
    magicLinkExpiresAt: {
      type: Date,
    },
    magicLinkUsed: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true }
);

modelSchema.methods.generateAccessToken = function () {
  return jwt.sign(
    {
      _id: this._id,
    },
    process.env.LOGIN_ACCESS_TOKEN_SECRET,
    {
      expiresIn: process.env.LOGIN_ACCESS_TOKEN_EXPIRY,
    }
  );
};
modelSchema.methods.generateRefreshToken = function () {
  return jwt.sign(
    {
      _id: this._id,
    },
    process.env.LOGIN_REFRESH_TOKEN_SECRET,
    {
      expiresIn: process.env.LOGIN_REFRESH_TOKEN_EXPIRY,
    }
  );
};

modelSchema.index({ agencyId: 1 });

const ModelUser = mongoose.model("ModelUser", modelSchema);

export default ModelUser;
