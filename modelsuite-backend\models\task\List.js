import mongoose from "mongoose";

const listSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, "List title is required!"],
  },
  boardId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Board",
    required: [true, "Board ID is required!"],
  },
  position: {
    type: Number,
    default: 0,
  },
  isArchived: {
    type: Boolean,
    default: false,
  },
  cards: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: "Card",
  }],
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for faster queries
listSchema.index({ boardId: 1, position: 1 });
listSchema.index({ boardId: 1, isArchived: 1 });

export default mongoose.model("List", listSchema); 