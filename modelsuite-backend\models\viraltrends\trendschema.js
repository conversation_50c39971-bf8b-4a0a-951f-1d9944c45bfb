import mongoose from "mongoose";
import express from "express";
const trendSchema = new mongoose.Schema({
  title: { type: String, required: true, maxlength: 255 },
  summary: { type: String, maxlength: 1000 },
  category: { type: String, default: 'Culture' },
  source: { type: String, required: true },
  url: { type: String },
  viral_score: { type: Number, default: 0 },
  region: { type: String, default: 'global' },
  tags: [String],
  created_at: { type: Date, default: Date.now },
  expires_at: { type: Date, default: () => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) }
});

// Create index for better performance
trendSchema.index({ created_at: -1 });
trendSchema.index({ region: 1 });
trendSchema.index({ category: 1 });

export const Trend = mongoose.model('Trend', trendSchema);

// Scoring algorithm
export function calculateViralScore(volume, velocity, relevance, geographic) {
  return (
    (volume || 0) * 0.4 +
    (velocity || 0) * 0.3 +
    (relevance || 0) * 0.2 +
    (geographic || 0) * 0.1
  );
}

// Relevance scoring for creators
export function getCreatorRelevanceScore(title, summary) {
  const creatorKeywords = [
    'tiktok', 'instagram', 'youtube', 'social media', 'influencer',
    'creator', 'viral', 'trending', 'meme', 'content', 'stream',
    'podcast', 'brand', 'marketing', 'ai', 'tech', 'ban', 'algorithm',
    'shorts', 'reels', 'stories', 'live', 'gaming', 'music'
  ];
  
  const text = `${title} ${summary}`.toLowerCase();
  let score = 0;
  
  creatorKeywords.forEach(keyword => {
    if (text.includes(keyword)) {
      score += 1;
    }
  });
  
  return Math.min(score / creatorKeywords.length * 10, 10);
}

// Auto-categorize trends
export function categorizeTrend(title, summary) {
  const text = `${title} ${summary}`.toLowerCase();
  
  if (text.match(/politic|trump|election|government|biden|congress/)) {
    return 'Politics';
  } else if (text.match(/tiktok|instagram|youtube|twitter|social|creator|influencer/)) {
    return 'Social Media';
  } else if (text.match(/ai|tech|app|software|cyber|digital|meta|google|apple/)) {
    return 'Tech';
  } else if (text.match(/meme|viral|trend|challenge|dance|funny/)) {
    return 'Memes';
  } else if (text.match(/ban|scandal|controversy|lawsuit|arrest|drama/)) {
    return 'Scandals';
  } else if (text.match(/celebrity|entertainment|movie|music|sports|gaming/)) {
    return 'Entertainment';
  } else {
    return 'Culture';
  }
}

// Extract tags from content
export function extractTags(title, summary, category) {
  const tags = [category];
  const text = `${title} ${summary}`.toLowerCase();
  
  const tagKeywords = {
    'viral': 'Viral',
    'trending': 'Trending',
    'breaking': 'Breaking',
    'celebrity': 'Celebrity',
    'technology': 'Technology',
    'entertainment': 'Entertainment',
    'sports': 'Sports',
    'music': 'Music',
    'gaming': 'Gaming',
    'news': 'News',
    'live': 'Live',
    'new': 'New'
  };
  
  Object.keys(tagKeywords).forEach(keyword => {
    if (text.includes(keyword)) {
      tags.push(tagKeywords[keyword]);
    }
  });
  
  return [...new Set(tags)];
}