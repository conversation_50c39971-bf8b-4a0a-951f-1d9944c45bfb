{"name": "modelsuite-backend", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon server.js", "start": "node server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^3.0.2", "cloudinary": "^1.41.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "docusign-esign": "^8.2.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "fast-csv": "^5.0.2", "googleapis": "^150.0.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.0", "multer": "^2.0.1", "multer-storage-cloudinary": "^4.0.0", "node-cron": "^4.2.1", "nodemailer": "^7.0.5", "puppeteer": "^24.14.0", "socket.io": "^4.8.1", "speakeasy": "^2.0.0", "tikapi": "^3.2.0", "twilio": "^5.7.3", "uuid": "^11.1.0", "xml2js": "^0.6.2"}, "devDependencies": {"nodemon": "^3.1.10"}}