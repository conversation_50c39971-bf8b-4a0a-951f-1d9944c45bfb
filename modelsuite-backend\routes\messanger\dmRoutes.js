import express from 'express';
import { verifyToken } from '../../middlewares/authMiddleware.js';
import { createNewDm, deleteAllDmsOfUser, fetchOlderDMMessages, getAllDMConversations } from '../../controllers/messages/dmController.js';
const router = express.Router();

router.get('/getAllDMConversations', verifyToken, getAllDMConversations)
router.post('/createNewDm', verifyToken, createNewDm);
router.get('/fetchOlderDmMessages', verifyToken, fetchOlderDMMessages);


router.delete('/deleteAllDmsOfUser', deleteAllDmsOfUser);


export default router;