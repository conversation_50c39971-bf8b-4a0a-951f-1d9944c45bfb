import { Router } from "express";
import {
  createNote,
  getNotesByModel,
  getNoteById,
  updateNote,
  deleteNote,
  togglePinNote,
  getNoteStats
} from "../controllers/noteController.js";
import { verifyToken } from "../middlewares/authMiddleware.js";

const router = Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Add this temporary route to noteRoutes.js for debugging
router.get('/debug/user', (req, res) => {
  res.json({
    user: req.user,
    role: req.user.role,
    id: req.user._id
  });
});


// Note CRUD operations
router.route("/").post(createNote); // POST /api/v1/notes - Create a new note
router.route("/model/:modelId").get(getNotesByModel); // GET /api/v1/notes/model/:modelId - Get notes for a specific model
router.route("/model/:modelId/stats").get(getNoteStats); // GET /api/v1/notes/model/:modelId/stats - Get note statistics for a model
router.route("/:noteId").get(getNoteById); // GET /api/v1/notes/:noteId - Get a specific note
router.route("/:noteId").patch(updateNote); // PATCH /api/v1/notes/:noteId - Update a note
router.route("/:noteId").delete(deleteNote); // DELETE /api/v1/notes/:noteId - Soft delete a note
router.route("/:noteId/pin").patch(togglePinNote); // PATCH /api/v1/notes/:noteId/pin - Toggle pin status

export default router;