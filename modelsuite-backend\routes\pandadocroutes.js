import express from "express";
import {
  getTemplates,
  createDocument,
  getDocumentById,
  handleWebhook,
  getContractsByAgency,
  getContractsByModelAndAgency
   } from "../controllers/pandadoccontroller.js";
import {verifyToken} from '../middlewares/authMiddleware.js';

const router = express.Router();

router.get("/templates", verifyToken,getTemplates);
router.post("/create-document", verifyToken,createDocument);
router.get("/documents/:id",verifyToken, getDocumentById);
router.post("/webhook", verifyToken,handleWebhook);
router.get("/agency/:agencyId",verifyToken, getContractsByAgency);
router.get("/model/:modelId/:agencyId",verifyToken, getContractsByModelAndAgency);



export default router;
