import express from 'express';
import { createUserProfile, getMyProfile, updateMyProfile, uploadAvatar, getUserProfileById, updateUserProfileById, uploadCoverPhoto, uploadPortfolioImage } from '../controllers/userProfileController.js';
import { verifyToken } from '../middlewares/authMiddleware.js';
import upload from '../middlewares/multer.js';
import { requireAdminOrAgency } from '../middlewares/roleCheck.js';

const router = express.Router();

// Create user profile (usually after registration, optional)
router.post('/', verifyToken, createUserProfile);

// Get current user's profile
router.get('/', verifyToken, getMyProfile);

// Update current user's profile
router.put('/', verifyToken, updateMyProfile);

// Upload avatar
router.post('/avatar', verifyToken, upload.single('avatar'), uploadAvatar);

// Upload cover photo
router.post('/cover', verifyToken, upload.single('cover'), uploadCoverPhoto);

// Upload portfolio image
router.post('/portfolio-image', verifyToken, uploadPortfolioImage);

// Get any user's profile by userId
router.get('/:userId', verifyToken, getUserProfileById);

// Update any user's profile by userId (admin/agency only)
router.put('/:userId', verifyToken, requireAdminOrAgency, updateUserProfileById);

export default router; 