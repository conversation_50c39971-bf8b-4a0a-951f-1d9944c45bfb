import express from "express";
import * as boardController from "../../controllers/task/boardController.js";
import * as listController from "../../controllers/task/listController.js";
import * as cardController from "../../controllers/task/cardController.js";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import multer from "multer";

const router = express.Router();

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow images, videos, and PDFs
    if (
      file.mimetype.startsWith('image/') ||
      file.mimetype.startsWith('video/') ||
      file.mimetype === 'application/pdf'
    ) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only images, videos, and PDFs are allowed.'));
    }
  }
});

// Apply auth middleware to all routes
router.use(verifyToken);

// Board routes
router.post("/creat-board", boardController.createBoard);
router.get("/boards", boardController.getAgencyBoards);
router.get("/boards/:boardId", boardController.getBoardById);
router.put("/boards/:boardId", boardController.updateBoard);
router.delete("/boards/:boardId", boardController.deleteBoard);

// List routes
router.post("/boards/:boardId/lists", listController.createList);
router.put("/lists/:listId", listController.updateList);
router.delete("/lists/:listId", listController.deleteList);

// Card routes
router.post("/lists/:listId/cards", cardController.createCard);
router.get("/cards/:cardId", cardController.getCardById);
router.put("/cards/:cardId", cardController.updateCard);
router.delete("/cards/:cardId", cardController.deleteCard);
router.put("/cards/:cardId/move", cardController.moveCard);
router.put("/cards/:cardId/hold", cardController.putCardOnHold);

// Individual tasks routes
router.get("/tasks", cardController.getIndividualTasks);
router.post("/tasks", cardController.createIndividualTask);

// Attachment routes
router.post("/cards/:cardId/attachments", upload.single('file'), cardController.addAttachment);
router.get("/cards/:cardId/attachments", cardController.getAttachments);
router.delete("/cards/:cardId/attachments/:attachmentId", cardController.deleteAttachment);

// Comment routes
router.get("/cards/:cardId/comments", cardController.getCardComments);
router.post("/cards/:cardId/comments", cardController.addComment);

export default router; 