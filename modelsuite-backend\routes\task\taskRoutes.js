import express from "express";
import * as taskController from "../../controllers/task/taskController.js";
import { verifyToken } from "../../middlewares/authMiddleware.js";
import multer from "multer";

const router = express.Router();

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow images, videos, and PDFs
    if (
      file.mimetype.startsWith('image/') ||
      file.mimetype.startsWith('video/') ||
      file.mimetype === 'application/pdf'
    ) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only images, videos, and PDFs are allowed.'));
    }
  }
});

// Apply auth middleware to all routes
router.use(verifyToken);

// Task routes
router.get("/list", taskController.getTasks);
router.post("/create", taskController.createTask);
router.put("/:taskId", taskController.updateTask);
router.delete("/:taskId", taskController.deleteTask);
router.put("/:taskId/hold", taskController.putTaskOnHold);

// Comment routes
router.get("/:taskId/comments", taskController.getComments);
router.post("/:taskId/comments", taskController.addComment);
router.delete("/:taskId/comments/:commentId", taskController.deleteComment);

// Attachment routes
router.get("/:taskId/attachments", taskController.getAttachments);
router.post("/:taskId/attachments", upload.single('file'), taskController.uploadAttachment);
router.delete("/:taskId/attachments/:attachmentId", taskController.deleteAttachment);

export default router; 