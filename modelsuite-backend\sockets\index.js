import Agency from '../models/agency.js';
import handleChatSockets from "./messanger/chatSocket.js";
import handleChannelSockets from "./messanger/channelSocket.js";
import handleDmSockets from "./messanger/dmSockets.js";
import handleGroupSockets from "./messanger/groupSockets.js";

const registerSocketHandlers = (io, socket, connectedUsers) => {
  let userId = null;
  let sessionStartTime = null;

  // Handle user connection
  socket.on('user:connect', async (data) => {
    try {
      userId = data.userId;
      sessionStartTime = new Date();
      
      // Update the user's session start time
      await Agency.findByIdAndUpdate(userId, {
        lastOnline: new Date(),
        sessionStartTime: sessionStartTime
      });
    } catch (error) {
      console.error('Error in user:connect:', error);
    }
  });

  // Handle periodic activity updates (every minute)
  socket.on('user:activity', async () => {
    try {
      if (userId && sessionStartTime) {
        const now = new Date();
        
        // Update the total active minutes and last online time
        await Agency.findByIdAndUpdate(userId, {
          lastOnline: now,
          $inc: { totalActiveMinutes: 1 } // Increment by 1 minute
        });
      }
    } catch (error) {
      console.error('Error in user:activity:', error);
    }
  });

  // Handle disconnection
  socket.on('disconnect', async () => {
    try {
      if (userId && sessionStartTime) {
        const now = new Date();
        const activeMinutes = Math.floor((now - sessionStartTime) / 60000);
        
        // Update final activity count
        await Agency.findByIdAndUpdate(userId, {
          lastOnline: now,
          sessionStartTime: null,
          $inc: { totalActiveMinutes: activeMinutes }
        });
      }
    } catch (error) {
      console.error('Error in disconnect:', error);
    }
  });

  // Register other socket handlers
  handleChatSockets(io, socket, connectedUsers);
  handleChannelSockets(io, socket, connectedUsers);
  handleDmSockets(io, socket, connectedUsers);
  handleGroupSockets(io, socket, connectedUsers);
};

export default registerSocketHandlers;
