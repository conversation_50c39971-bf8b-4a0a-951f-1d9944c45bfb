import Agency from "../../models/agency.js";
import ModelUser from "../../models/model.js";   

const handleChatSockets = (io, socket, connectedUsers) => {

   socket.on('fetch_user_status', async (userIds) => {
  try {
    // userIds is an array of IDs
    const statusList = await Promise.all(
      userIds.map(async (userId) => {
        const isOnline = connectedUsers.has(userId);
        let lastOnline = null;

        if (!isOnline) {
          let user = await ModelUser.findById(userId).select("lastOnline");
          if (!user) {
            user = await Agency.findById(userId).select("lastOnline");
          }
          if (user) {
            lastOnline = user.lastOnline;
          }
        }

        return {
          userId,
          isOnline,
          lastOnline,
        };
      })
    );

    // Emit back to the requester
    socket.emit('update_user_status', statusList);


  } catch (err) {
    console.error('❌ Error in fetch_user_status:', err.message);
  }
});



}


export default handleChatSockets;
