import DmMessage from "../../models/messanger/dmMessages.js";

const handleDmSockets = (io, socket, connectedUsers) => {

  socket.on("dm:addNewConversation", (data) =>{
    const targetedUserSocket = connectedUsers.get(data.targetUserId)
    io.to(targetedUserSocket).emit('dm:add_incoming_dm', data.dmConvo)
  })




  socket.on("dm:send_message", async (messageData) => {
    try {
      // Create and save message to DB
      const newMessage = await DmMessage.create({
        _id: messageData._id,
        convoId: messageData.convoId,
        senderId: messageData.senderId,
        receiverId: messageData.receiverId,
        text: messageData.text,
        type: messageData.type,
        status: 'sent',
        attachments: messageData.attachments,
        reactions: messageData.reactions,
        createdAt: messageData.createdAt
      });

    

      // Prepare message to send back
      const messageToSend = newMessage.toObject()
     
    
      // get receiver's socket id
      const receiverSocketId = connectedUsers.get(
        messageData.receiverId.toString()
      );

      // emit only if online
      if (receiverSocketId) {
        io.to(receiverSocketId).emit("dm:new_message", messageToSend);
     
      }

      // also emit back to sender so sender's UI updates immediately
      const senderSocketId = connectedUsers.get(
        messageData.senderId.toString()
      );
      if (senderSocketId) {
        io.to(senderSocketId).emit("dm:message_sent", messageToSend);
      }
    } catch (err) {
      console.error("Error saving DM message:", err);
    }
  });



  socket.on("dm:typing", ({ convoId, userId, state }) => {
    socket.broadcast.emit("dm:set_typing", {
      convoId,
      userId, // who is typing
      state, // true or false
    });
  });



// For batching seen updates
const seenMessageBuffer = new Map(); // userId => [messageId]
const seenTimers = new Map(); // userId => timeoutId

socket.on("dm:message_seen", async ({ convoId, messageId, userId }) => {
  try {
    const message = await DmMessage.findById(messageId);

    if (!message) return;

    const senderId = message.senderId?.toString();

    // Don't notify self
    if (senderId && senderId !== userId) {
      const senderSocketId = connectedUsers.get(senderId);
      if (senderSocketId) {
        io.to(senderSocketId).emit("dm:message_seen", {
          convoId,
          messageId,
        });
       
      }
    }

    // Batch update buffer
    if (!seenMessageBuffer.has(userId)) {
      seenMessageBuffer.set(userId, []);
    }
    seenMessageBuffer.get(userId).push(messageId);

    // Reset debounce timer
    if (seenTimers.has(userId)) {
      clearTimeout(seenTimers.get(userId));
    }

    const timer = setTimeout(async () => {
      const messageIds = seenMessageBuffer.get(userId);
      if (messageIds && messageIds.length > 0) {
        await DmMessage.updateMany(
          { _id: { $in: messageIds } },
          { $set: { status: "seen" } }
        );
      
        seenMessageBuffer.delete(userId);
        seenTimers.delete(userId);
      }
    }, 2000);

    seenTimers.set(userId, timer);

  } catch (err) {
    console.error("❌ Error in dm:message_seen:", err);
  }
});



};

export default handleDmSockets;
