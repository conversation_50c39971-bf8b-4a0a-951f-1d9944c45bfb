import PDFGenerator from '../utils/pdfGenerator.js';

// Test data
const mockModelData = {
  fullName: "Test Model",
  email: "<EMAIL>",
  phone: "+1234567890"
};

const mockTemplateData = {
  title: "Test Template",
  sections: [
    {
      title: "Personal Information",
      questions: [
        {
          _id: "q1",
          label: "What is your age?",
          type: "number"
        },
        {
          _id: "q2",
          label: "What are your hobbies?",
          type: "multi-select"
        }
      ]
    }
  ]
};

const mockAnswers = [
  {
    questionId: "q1",
    answer: 25
  },
  {
    questionId: "q2",
    answer: ["Reading", "Swimming", "Photography"]
  }
];

async function testPDFGeneration() {
  try {
    console.log('Starting PDF generation test...');
    
    const pdf = await PDFGenerator.generateQuestionnaireReport(
      mockModelData,
      mockAnswers,
      mockTemplateData
    );
    
    console.log('PDF generated successfully!');
    console.log('PDF Buffer length:', pdf.length);
    
    // Cleanup
    await PDFGenerator.cleanup();
    console.log('Test completed successfully!');
    
  } catch (error) {
    console.error('Test failed:', error);
    await PDFGenerator.cleanup();
    process.exit(1);
  }
}

// Only run test if this file is executed directly
if (process.argv[1].endsWith('test-pdf.js')) {
  testPDFGeneration();
}

export { testPDFGeneration };
