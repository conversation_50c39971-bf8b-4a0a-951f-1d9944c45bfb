import { google } from "googleapis";

export const insertEventToGoogleCalendar = async (modelUser, eventData) => {
  const oauth2Client = new google.auth.OAuth2(
    process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    process.env.GOOGLE_REDIRECT_URI
  );

  oauth2Client.setCredentials({
    access_token: modelUser.googleAccessToken,
    refresh_token: modelUser.googleRefreshToken,
  });

  const calendar = google.calendar({ version: "v3", auth: oauth2Client });

  // 🟡 Base event structure
  const event = {
    summary: eventData.title,
    description: eventData.description,
    start: {
      dateTime: eventData.start,
      timeZone: eventData.timezone,
    },
    end: {
      dateTime: eventData.end,
      timeZone: eventData.timezone,
    },
  };

  // ✅ If guests are provided, attach them and enable Meet
  if (eventData.guests && eventData.guests.length > 0) {
    event.attendees = eventData.guests.map((email) => ({ email }));
    event.conferenceData = {
      createRequest: {
        requestId: `meet-${Date.now()}`,
        conferenceSolutionKey: { type: "hangoutsMeet" },
      },
    };
  }

  const response = await calendar.events.insert({
    calendarId: "primary",
    resource: event,
    conferenceDataVersion: event.conferenceData ? 1 : 0,
    sendUpdates: "all",
  });

  return response.data; // includes Google Event ID, Meet link if created
};
