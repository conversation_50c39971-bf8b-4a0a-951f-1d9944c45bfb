import PDFGenerator from './pdfGenerator.js';

// Graceful shutdown handler
process.on('SIGINT', async () => {
  console.log('Gracefully shutting down PDF generator...');
  await PDFGenerator.cleanup();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Gracefully shutting down PDF generator...');
  await PDFGenerator.cleanup();
  process.exit(0);
});

export default PDFGenerator;
