import nodemailer from 'nodemailer';

export const sendOtpToEmail = async (email, otp) => {
  try {
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: email,
      subject: 'OTP for ModelSuite.ai',
      text: `Your OTP for is: ${otp}. It expires in 10 minutes.`,
    });

    // console.log(`OTP sent to email: ${email}`);
  } catch (error) {
    console.error('Failed to send OTP via Email:', error.message);
    throw new Error('<PERSON><PERSON> failed');
  }
};
