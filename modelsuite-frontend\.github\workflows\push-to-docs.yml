name: 📘 Auto Changelog to <PERSON>s

on:
  push:
    branches:
      - Development  # Trigger when new code is pushed to Development

env:
  REPO_NAME: modelsuite-frontend 

jobs:
  update-docs:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout this repo
        uses: actions/checkout@v3

      - name: Generate changelog block
        run: |
          DATE=$(date +'%Y-%m-%d')
          LOG=$(git log -10 --pretty=format:"- %s (%an)")

          echo "<Update label=\"$DATE\" description=\"$REPO_NAME - Auto update\">" > update.mdx
          echo "$LOG" >> update.mdx
          echo "</Update>\n" >> update.mdx

      - name: Clone Mintlify Docs repo
        run: |
          git clone https://${{ secrets.DOCS_TOKEN }}@github.com/modelsuite-ai/Docs.git docs-clone

      - name: Prepend changelog entry
        run: |
          cd docs-clone
          cat ../update.mdx changelog.mdx > combined.mdx
          mv combined.mdx changelog.mdx

      - name: Commit and push to Docs
        run: |
          cd docs-clone
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"
          git add changelog.mdx
          git commit -m "🔄 Auto-update changelog from $REPO_NAME"
          git push
