# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.1.0/),  
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

---

## [Unreleased]

<!-- Add upcoming changes here -->

---

## [0.0.2] - 2025-07-16

### Added
- Forgot password feature.

### Changed
- Minor UI changes to login, registration, and forgot password input fields.

### Fixed
- Bug fixes in agency authentication.

### Removed
- Removed unused top navbar.

---

## [Unreleased]

<!-- Add upcoming changes here -->

---

## [0.0.1] - 2025-07-15

### Added
- Full calendar integration using `FullCalendar` in model dashboard.
- Google Calendar connection UI with meet link display.
- Global menu component for agency dashboard navigation.

### Fixed
- Resolved calendar visibility and layout issue on dashboard.

---

[Unreleased]: https://github.com/modelsuite-ai/modelsuite-frontend/compare/0.0.2...HEAD  
[0.0.2]: https://github.com/modelsuite-ai/modelsuite-frontend/releases/tag/0.0.2  
[0.0.1]: https://github.com/modelsuite-ai/modelsuite-frontend/releases/tag/0.0.1
