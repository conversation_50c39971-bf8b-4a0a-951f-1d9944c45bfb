import React from "react";
import moment from "moment"; // npm install moment

const InvoiceTable = React.memo(({ invoices = [], loading, onEdit }) => {
  return (
    <div className="overflow-x-auto scrollBar bg-gray-800 rounded-lg shadow">
      <table className="min-w-full divide-y divide-gray-700">
        <thead className="bg-gray-700">
          <tr>
            {[
              "Client",
              "Campaign",
              "Amount",
              "Curr.",
              "Status",
              "Invoice Date",
              "Due Date",
              "Download",
              "Actions", // ✅ New column
            ].map((col) => (
              <th
                key={col}
                className="px-6 py-3 text-left text-xs font-medium text-gray-300"
              >
                {col}
              </th>
            ))}
          </tr>
        </thead>

        <tbody className="divide-y divide-gray-700">
          {loading ? (
            <tr>
              <td colSpan={9} className="text-center text-gray-400 py-4">
                <div className="flex justify-center items-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                </div>
              </td>
            </tr>
          ) : invoices.length === 0 ? (
            <tr>
              <td colSpan={9} className="text-center text-gray-400 py-4">
                No invoices found.
              </td>
            </tr>
          ) : (
            invoices.map((inv) => (
              <tr key={inv._id}>
                <td className="px-6 py-4">{inv.client?.fullName || "N/A"}</td>
                <td className="px-6 py-4">{inv.campaign}</td>
                <td className="px-6 py-4">{inv.amount}</td>
                <td className="px-6 py-4">{inv.currency}</td>

                {/* ✅ Show status normally */}
                <td
                  className={`px-6 py-4 ${
                    inv.status === "Paid"
                      ? "text-green-400"
                      : inv.status === "Unpaid"
                      ? "text-yellow-400"
                      : "text-red-400"
                  }`}
                >
                  {inv.status}
                </td>

                <td className="px-6 py-4">
                  {moment(inv.createdAt).format("YYYY-MM-DD")}
                </td>
                <td className="px-6 py-4">
                  {moment(inv.dueDate).format("YYYY-MM-DD")}
                </td>

                {/* ✅ Download file */}
                <td className="px-6 py-4">
                  {inv.fileUrl ? (
                    <a
                      href={inv.fileUrl}
                      download
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex justify-center items-center text-blue-400 hover:underline"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M4 16v2a2 2 0 002 2h12a2 2 0 002-2v-2M7 10l5 5m0 0l5-5m-5 5V4"
                        />
                      </svg>
                    </a>
                  ) : (
                    <span className="text-gray-400">No file</span>
                  )}
                </td>

                {/* ✅ NEW ACTION COLUMN */}
                <td className="px-6 py-4">
                  {inv.status === "Unpaid" ? (
                    <button
                      onClick={() => onEdit?.(inv._id, { status: "Paid" })}
                      className="bg-yellow-500 hover:bg-green-500 text-black px-3 py-1 rounded-md transition"
                    >
                      Mark as Paid
                    </button>
                  ) : (
                    <span className="text-sm text-gray-500">No action</span>
                  )}
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
});

export default InvoiceTable;
