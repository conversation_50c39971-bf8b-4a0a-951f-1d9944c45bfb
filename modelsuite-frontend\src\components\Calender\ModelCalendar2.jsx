"use client"
import { useState, useEffect, useRef } from "react"
import FullCalendar from "@fullcalendar/react"
import dayGridPlugin from "@fullcalendar/daygrid"
import interactionPlugin from "@fullcalendar/interaction"
import axios from "axios"
import { toast } from "react-hot-toast";
import { CalendarPlus, Clock, MapPin, Plus, Calendar, CalendarX2, Loader2 } from "lucide-react"
import EventModal from "./components/EventModal"

const CalendarView = ({ modelId, isModel }) => {
  const [events, setEvents] = useState([])
  const [loading, setLoading] = useState(true)
  const [showEventModal, setShowEventModal] = useState(false)
  const [selectedDate, setSelectedDate] = useState(null)
  const [selectedEvent, setSelectedEvent] = useState(null)
  const [currentView, setCurrentView] = useState("dayGridMonth")
  const [connected, setConnected] = useState(null)
  const baseUrl = import.meta.env.VITE_API_BASE_URL
  const calendarRef = useRef(null)

  useEffect(() => {
    const checkStatus = async () => {
      try {
        const res = await axios.get(`${baseUrl}/google/status/${modelId}`)
        setConnected(res.data.connected)
      } catch {
        setConnected(false)
      }
    }
    checkStatus()
  }, [modelId])

  useEffect(() => {
    if (connected) {
      fetchEvents()
    }
  }, [modelId, connected])

  const fetchEvents = async () => {
    try {
      setLoading(true)
      const response = await axios.get(`${baseUrl}/event/get/${modelId}`)
      const formattedEvents = response.data.map((event) => ({
        id: event.id,
        title: event.title,
        start: event.start,
        end: event.end,
        description: event.description,
        timezone: event.timezone,
        extendedProps: {
          meetLink: event.meetLink,
          guests: event.guests || [], // ✅ Add this line
        },
      }))
      setEvents(formattedEvents)
    } catch (error) {
      console.error("Error fetching events:", error)
      toast.error("Failed to fetch calendar events")
    } finally {
      setLoading(false)
    }
  }

  const handleConnectClick = () => {
    const token = JSON.parse(localStorage.getItem("auth"))?.token
    window.location.href = `${baseUrl}/google/auth/google?state=${token}`
  }

  const handleDisconnect = async () => {
    try {
      const token = JSON.parse(localStorage.getItem("auth"))?.token
      await axios.delete(`${baseUrl}/google/disconnect`, {
        headers: { Authorization: `Bearer ${token}` },
      })
      setConnected(false)
      toast.success("Calendar disconnected successfully")
    } catch (err) {
      toast.error(err.response?.data?.error || "Something went wrong")
    }
  }

  const handleDateClick = (arg) => {
    if (!isModel) {
      setSelectedDate(arg.date)
      setSelectedEvent(null)
      setShowEventModal(true)
    }
  }

  const handleEventClick = (clickInfo) => {
    setSelectedEvent(clickInfo.event)
    setShowEventModal(true)
  }

  const handleEventCreate = async (eventData) => {
    try {
      const response = await axios.post(`${baseUrl}/event/create`, {
        modelId,
        ...eventData,
      })
      toast.success("Event created successfully!")
      setShowEventModal(false)
      fetchEvents()
      if (response.data.meetLink) {
        toast.success("Google Meet link created!")
      }
    } catch (error) {
      console.error("Error creating event:", error)
      toast.error(error.response?.data?.message || "Failed to create event")
    }
  }

  const handleViewChange = (newView) => {
    setCurrentView(newView)
    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi()
      calendarApi.changeView(newView)
    }
  }

  // Get next upcoming event
  const getNextEvent = () => {
    const now = new Date()
    const upcomingEvents = events
      .filter((event) => new Date(event.start) > now)
      .sort((a, b) => new Date(a.start) - new Date(b.start))
    return upcomingEvents[0] || null
  }

  // Custom event content renderer
  const renderEventContent = (eventInfo) => {
    const title = eventInfo.event.title
    const maxLength = currentView === "dayGridMonth" ? 15 : 25
    const truncatedTitle = title.length > maxLength ? title.substring(0, maxLength) + "..." : title
    return (
      <div className="fc-event-custom" title={title}>
        <span className="fc-event-title-custom">{truncatedTitle}</span>
      </div>
    )
  }

  // Loading state for checking connection
  if (connected === null) {
    return (
      <div className="flex items-center justify-center py-20">
        <Loader2 className="animate-spin h-8 w-8 text-[#a78bfa]" />
        <span className="ml-3 text-gray-400">Checking calendar status...</span>
      </div>
    )
  }

  // Not connected state
if (!connected) {
  if (isModel) {
    return (
      <div className="relative min-h-screen bg-gradient-to-br from-[#181c2a] via-[#23293a] to-[#1e2233] py-12 px-2">
        <div className="max-w-2xl mx-auto rounded-3xl shadow-2xl border border-[#35374a] bg-[rgba(36,41,58,0.92)] backdrop-blur-xl p-10">
          <div className="text-center space-y-6">
            <CalendarPlus className="mx-auto text-[#a78bfa] w-16 h-16" />
            <h2 className="text-2xl font-bold text-white">Google Calendar Not Connected</h2>
            <p className="text-gray-400">Connect to sync events and receive updates.</p>
            <button
              onClick={handleConnectClick}
              className="px-6 py-3 bg-gradient-to-r from-[#a78bfa] to-[#8b5cf6] hover:from-[#b993ff] hover:to-[#8ca6db] text-white rounded-2xl shadow-lg font-semibold transition-all duration-200 ring-2 ring-[#a78bfa] ring-offset-2 ring-offset-[#23293a]"
              style={{ boxShadow: "0 4px 24px 0 rgba(167,139,250,0.18)" }}
            >
              Connect Google Calendar
            </button>
          </div>
        </div>
      </div>
    )
  } else {
    return (
      <div className="relative min-h-screen bg-gradient-to-br from-[#181c2a] via-[#23293a] to-[#1e2233] py-12 px-2">
        <div className="max-w-2xl mx-auto rounded-3xl shadow-2xl border border-[#35374a] bg-[rgba(36,41,58,0.92)] backdrop-blur-xl p-10">
          <div className="text-center space-y-6">
            <CalendarX2 className="mx-auto text-red-400 w-16 h-16" />
            <h2 className="text-2xl font-bold text-white">Model Calendar Not Connected</h2>
            <p className="text-gray-400">This model hasn't connected their Google Calendar yet.</p>
          </div>
        </div>
      </div>
    )
  }
}


  if (loading) {
    return (
      <div className="flex items-center justify-center py-20">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#a78bfa]"></div>
        <span className="ml-3 text-gray-400">Loading calendar...</span>
      </div>
    )
  }

  const nextEvent = getNextEvent()

  return (
    <div className="max-w-7xl mx-auto px-4">
      <div className="relative bg-gradient-to-br from-[#181c2a] via-[#23293a] to-[#1e2233] py-8 px-2">
        {connected && (
          <div className="max-w-7xl mx-auto rounded-3xl shadow-2xl border border-[#35374a] bg-[rgba(36,41,58,0.92)] backdrop-blur-xl p-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 h-full">
            {/* Left Section - Upcoming Events */}
            <div className="flex flex-col">
              {/* Create New Event Button */}
              <div className="mb-6">
                {!isModel && (
                  <button
                    onClick={() => {
                      setSelectedDate(new Date())
                      setSelectedEvent(null)
                      setShowEventModal(true)
                    }}
                    className="w-full flex items-center justify-center gap-2 px-6 py-4 bg-gradient-to-r from-[#a78bfa] to-[#8b5cf6] hover:from-[#b993ff] hover:to-[#8ca6db] text-white rounded-2xl shadow-lg font-semibold text-lg transition-all duration-200 ring-2 ring-[#a78bfa] ring-offset-2 ring-offset-[#23293a]"
                    style={{ boxShadow: "0 4px 24px 0 rgba(167,139,250,0.18)" }}
                  >
                    <Plus className="w-6 h-6" />
                    New Event
                  </button>
                )}
              </div>

              {/* Upcoming Events List */}
              <div className="flex-1 bg-[rgba(36,41,58,0.98)] rounded-2xl border border-[#35374a] shadow-lg p-6">
                <h3 className="text-2xl font-bold text-[#a78bfa] mb-4 tracking-tight">Upcoming Event</h3>
                <p className="text-gray-400 text-sm mb-6">Create events to share for people to book on your calendar.</p>

                <div className="space-y-4 max-h-[650px] overflow-y-auto custom-scrollbar pr-2">
                  {events.length === 0 ? (
                    <div className="text-center py-12 text-[#a78bfa] flex flex-col items-center justify-center">
                      <CalendarPlus className="w-12 h-12 mb-4 opacity-60 text-[#a78bfa]" />
                      <p className="text-lg font-semibold">No events scheduled</p>
                    </div>
                  ) : (
                    events.map((event) => (
                      <EventTypeCard key={event.id} event={event} onClick={() => handleEventClick({ event })} />
                    ))
                  )}
                </div>
              </div>
            </div>

            {/* Right Section - Next Event + Calendar */}
            <div className="flex flex-col gap-6">
              {/* Next Event Section */}
              <div className="bg-[rgba(36,41,58,0.98)] rounded-2xl border border-[#35374a] shadow-lg p-6">
                <h3 className="text-2xl font-bold text-[#a78bfa] mb-4 tracking-tight">Next Event</h3>

                <h3 className="text-2xl font-bold text-[#a78bfa] mb-4 tracking-tight">
                  {nextEvent ? nextEvent.title : "No Upcoming Events"}
                </h3>

                {nextEvent ? (
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 text-gray-300">
                      <Clock className="w-5 h-5 text-[#a78bfa]" />
                      <span className="text-lg font-semibold">
                        {Math.round((new Date(nextEvent.end) - new Date(nextEvent.start)) / (1000 * 60))} Min
                      </span>
                    </div>

                    {nextEvent.extendedProps?.meetLink && (
                      <div className="flex items-center gap-3 text-gray-300">
                        <MapPin className="w-5 h-5 text-[#a78bfa]" />
                        <span className="text-lg">Google Meet</span>
                      </div>
                    )}

                    <div className="flex items-center gap-3 text-gray-300">
                      <Calendar className="w-5 h-5 text-[#a78bfa]" />
                      <span className="text-lg">One on One</span>
                    </div>

                    <div className="pt-3 border-t border-[#35374a]">
                      <p className="text-gray-400 text-sm">
                        {new Date(nextEvent.start).toLocaleDateString("en-US", {
                          weekday: "long",
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        })}
                      </p>
                      <p className="text-[#a78bfa] font-semibold text-lg">
                        {new Date(nextEvent.start).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                        {nextEvent.end &&
                          ` - ${new Date(nextEvent.end).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}`}
                      </p>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-400">No upcoming events scheduled</p>
                )}
              </div>

              {/* Calendar Section */}
              <div className="bg-[rgba(36,41,58,0.98)] rounded-2xl border border-[#35374a] shadow-lg p-6">
                {/* View Toggle Buttons */}
                <div className="flex justify-center mb-4">
                  <div className="inline-flex rounded-full border border-[#35374a] bg-[#23293a] p-2 shadow-lg gap-2">
                    <button
                      onClick={() => handleViewChange("dayGridMonth")}
                      className={`px-6 py-2 text-sm font-bold rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-[#a78bfa] focus:ring-offset-2 focus:ring-offset-[#23293a] ${
                        currentView === "dayGridMonth"
                          ? "bg-gradient-to-r from-[#a78bfa] to-[#8b5cf6] text-white shadow-lg ring-4 ring-[#a78bfa]"
                          : "text-[#a78bfa] hover:bg-[#23293a]"
                      }`}
                    >
                      Month
                    </button>
                    <button
                      onClick={() => handleViewChange("dayGridWeek")}
                      className={`px-6 py-2 text-sm font-bold rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-[#a78bfa] focus:ring-offset-2 focus:ring-offset-[#23293a] ${
                        currentView === "dayGridWeek"
                          ? "bg-gradient-to-r from-[#a78bfa] to-[#8b5cf6] text-white shadow-lg ring-4 ring-[#a78bfa]"
                          : "text-[#a78bfa] hover:bg-[#23293a]"
                      }`}
                    >
                      Week
                    </button>
                  </div>
                </div>

                <div className="calendar-container">
                  <FullCalendar
                    ref={calendarRef}
                    plugins={[dayGridPlugin, interactionPlugin]}
                    initialView={currentView}
                    events={events}
                    dateClick={handleDateClick}
                    eventClick={handleEventClick}
                    eventContent={renderEventContent}
                    height={currentView === "dayGridWeek" ? 380 : 450}
                    headerToolbar={{
                      left: "prev,next today",
                      center: "title",
                      right: "",
                    }}
                    eventDisplay="block"
                    dayMaxEvents={currentView === "dayGridWeek" ? 2 : 3}
                    moreLinkClick="popover"
                    eventClassNames="cursor-pointer"
                  />
                </div>

                {/* Custom FullCalendar Styles */}
                <style jsx>{`
                  .calendar-container {
                    max-height: 450px;
                    overflow: hidden;
                  }
                  .fc {
                    overflow: hidden !important;
                  }
                  .fc-view-harness {
                    overflow: hidden !important;
                  }
                  .fc-scroller {
                    overflow: hidden !important;
                  }
                  .fc-col-header-cell {
                    background-color: rgba(36,41,58,0.92) !important;
                    border-color: #35374a !important;
                    backdrop-filter: blur(8px);
                  }
                  .fc-col-header-cell-cushion {
                    color: #a78bfa !important;
                    font-weight: 700 !important;
                    padding: 8px 6px !important;
                    font-size: 0.9rem !important;
                  }
                  .fc-daygrid-day-number {
                    color: #a78bfa !important;
                    font-weight: 600 !important;
                    font-size: 0.9rem !important;
                  }
                  .fc-day-today {
                    background: linear-gradient(90deg, #a78bfa 0%, #8b5cf6 100%) !important;
                    border-radius: 12px !important;
                  }
                  .fc-day-today .fc-daygrid-day-number {
                    color: #fff !important;
                    font-weight: 700 !important;
                  }
                  .fc-daygrid-day {
                    border-color: #35374a !important;
                    height: ${currentView === "dayGridWeek" ? "45px" : "60px"} !important;
                  }
                  .fc-event-custom {
                    padding: 2px 8px !important;
                    border-radius: 6px !important;
                    font-size: 12px !important;
                    line-height: 1.3 !important;
                    overflow: hidden !important;
                    white-space: nowrap !important;
                    text-overflow: ellipsis !important;
                    background: linear-gradient(90deg, #a78bfa 0%, #8b5cf6 100%) !important;
                    border: none !important;
                    color: #fff !important;
                    cursor: pointer !important;
                    margin-bottom: 2px !important;
                    box-shadow: 0 2px 8px 0 rgba(167,139,250,0.15);
                  }
                  .fc-event-title-custom {
                    font-weight: 600 !important;
                    font-size: 12px !important;
                    overflow: hidden !important;
                    text-overflow: ellipsis !important;
                    white-space: nowrap !important;
                    display: block !important;
                  }
                  .fc-daygrid-event {
                    margin-top: 2px !important;
                    margin-bottom: 2px !important;
                  }
                  .fc-event {
                    border: none !important;
                    background: linear-gradient(90deg, #a78bfa 0%, #8b5cf6 100%) !important;
                    color: #fff !important;
                  }
                  .fc-event:hover {
                    background: #a78bfa !important;
                  }
                  .fc-toolbar {
                    margin-bottom: 10px !important;
                  }
                  .fc-toolbar h2 {
                    font-size: 1.2rem !important;
                    color: #a78bfa !important;
                  }
                  .fc-button {
                    background: #a78bfa !important;
                    border-color: #a78bfa !important;
                    color: white !important;
                  }
                  .fc-button:hover {
                    background: #8b5cf6 !important;
                    border-color: #8b5cf6 !important;
                  }
                  .fc-daygrid-week .fc-daygrid-day {
                    height: 45px !important;
                  }
                  .fc-daygrid-week .fc-event-custom {
                    font-size: 11px !important;
                    padding: 1px 6px !important;
                  }
                `}</style>
              </div>
            </div>
          </div>

          {/* Event Modal */}
          {showEventModal && (
            <EventModal
              isOpen={showEventModal}
              onClose={() => setShowEventModal(false)}
              onSave={handleEventCreate}
              selectedDate={selectedDate}
              selectedEvent={selectedEvent}
              canEdit={!isModel}
            />
          )}
        </div>
        )}
        
      </div>

      {/* Connection Status Section */}
      {connected && isModel && (
        <div className="bg-[rgba(36,41,58,0.98)] rounded-2xl border border-[#35374a] shadow-lg p-6 max-w-2xl mx-auto mt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-green-400 font-medium">
              <CalendarPlus className="w-5 h-5" />
              <p>Calendar is connected and syncing automatically</p>
            </div>
            <button
              onClick={handleDisconnect}
              className="px-4 py-2 border border-red-500 text-red-400 rounded-lg hover:bg-red-900/20 transition"
            >
              <div className="flex items-center justify-center gap-2">
                <CalendarX2 className="w-4 h-4" />
                <span>Disconnect</span>
              </div>
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

const EventTypeCard = ({ event, onClick }) => {
  const startDate = new Date(event.start)
  const endDate = new Date(event.end)
  const duration = Math.round((endDate - startDate) / (1000 * 60))

  return (
    <div
      onClick={onClick}
      className="bg-[#23293a] border border-[#35374a] rounded-xl p-4 cursor-pointer hover:shadow-lg transition-all group"
    >
      <div className="flex justify-between items-start mb-3">
        <h4 className="font-semibold text-white text-lg group-hover:text-[#a78bfa] transition-colors">{event.title}</h4>
        <button className="text-gray-400 hover:text-white">
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
          </svg>
        </button>
      </div>

      {event.description && <p className="text-gray-400 text-sm mb-3 line-clamp-2">{event.description}</p>}

      <div className="space-y-2 text-sm text-gray-400 mb-4">
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-[#a78bfa]" />
          <span>{startDate.toLocaleDateString()}</span>
        </div>
        <div className="flex items-center gap-2">
          <Clock className="w-4 h-4 text-[#a78bfa]" />
          <span>
            {startDate.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
            {endDate && ` - ${endDate.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}`}
          </span>
        </div>
        {event.extendedProps?.meetLink && (
          <div className="flex items-center gap-2">
            <MapPin className="w-4 h-4 text-[#a78bfa]" />
            <span className="text-[#a78bfa]">Google Meet</span>
          </div>
        )}
      </div>

      <button className="w-full bg-[#6366f1] hover:bg-[#5855eb] text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
        View event
      </button>
    </div>
  )
}

export default CalendarView