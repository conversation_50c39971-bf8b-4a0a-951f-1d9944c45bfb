import React, { useState, useRef, useEffect } from 'react';
import { Camera, User, Briefcase, MapPin, Info, Clock } from 'lucide-react';
import { FaInstagram, FaTiktok, FaYoutube, FaImage } from 'react-icons/fa';
import Navbar from '../Navbar';
import Sidebar from '../../pages/Sidebar/Sidebar';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import Gallery from './Gallery';
import Tooltip from '../ui/Tooltip';

export default function ProfileForm() {
  const [formData, setFormData] = useState({
    display_name: '',
    email: '',
    phone: '',
    country: '',
    city: '',
    bio: '',
    job_title: '',
    role_description: '',
    status: '',
    avatar_url: '',
    cover_url: '',
    social_links: {
      instagram: '',
      tiktok: '',
      youtube: '',
    },
    availability_text: '',
    reachability: '',
    last_active: '', // ISO string or timestamp
    hide_last_active: false, // privacy option
    what_i_do: '',
    passions: '',
    communication_style: '',
    languages: [],
    fun_fact: '',
  });
  const [profileImage, setProfileImage] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [errors, setErrors] = useState({});
  const fileInputRef = useRef(null);
  const [toast, setToast] = useState({ message: '', type: '' });
  const [coverPreview, setCoverPreview] = useState(null);
  const coverInputRef = useRef(null);
  const [mode, setMode] = useState('view'); // 'view' or 'edit'
  const [showAvatarModal, setShowAvatarModal] = useState(false);
  const [avatarModalVisible, setAvatarModalVisible] = useState(false); // for mount/unmount
  const [avatarModalAnim, setAvatarModalAnim] = useState('in'); // 'in' or 'out'
  const formRef = useRef(null);

  // Add job title options
  const jobTitleOptions = [
    '',
    'Model',
    'Fashion Model',
    'Commercial Model',
    'Runway Model',
    'Fitness Model',
    'Promotional Model',
    'Influencer',
    'Actor',
    'Other',
  ];
  // Remove avatarOutAnim state and all related overlay logic
  // Restore modal close logic: on close, set avatarModalAnim('out'), setTimeout(() => setAvatarModalVisible(false), 300)
  // Only render the modal (with backdrop and container) when avatarModalVisible is true
  // Remove handleAvatarModalClose and use inline handlers as before

  // 1. Remove portfolio state, useEffect, and all handlers (handlePortfolioUpload, handlePortfolioReorder, handlePortfolioDelete).
  // 2. Remove all Portfolio section JSX from both view and edit modes.
  // 3. Remove all drag-and-drop (dnd-kit) imports and SortableImage component.
  // 4. Remove all portfolio upload logic and handlers.

  const [galleryImages, setGalleryImages] = useState([]);
  // Remove galleryManageMode state and Manage Gallery button. Always render Gallery with editable={true}.
  // Replace:
  // <Gallery images={galleryImages} onChange={setGalleryImages} editable={galleryManageMode} />
  // ...Manage Gallery button...
  // With:
  // <Gallery images={galleryImages} onChange={setGalleryImages} editable={true} />
  // 4. Remove all portfolio upload logic and handlers.

  // Add galleryManageMode state at the top of ProfileForm:
  // Remove galleryManageMode and related logic

  // Add agencyName variable at the top of the component
  const agencyName = 'ModelSuite'; // Replace with dynamic value if available

  // Fetch profile on mount
  useEffect(() => {
    const fetchProfile = async () => {
      setLoading(true);
      setError('');
      try {
        const token = JSON.parse(localStorage.getItem('auth'))?.token;
        const res = await fetch(`${import.meta.env.VITE_API_BASE_URL}/profile/`, {
          headers: { Authorization: `Bearer ${token}` },
        });
        if (!res.ok) throw new Error('Failed to fetch profile');
        const data = await res.json();
        setFormData({
          display_name: data.display_name || '',
          email: data.email || '',
          phone: data.phone || '',
          country: data.country || '',
          city: data.city || '',
          bio: data.bio || '',
          job_title: data.job_title || '',
          role_description: data.role_description || '',
          status: data.status || '',
          avatar_url: data.avatar_url || '',
          cover_url: data.cover_url || '',
          social_links: data.social_links || { instagram: '', tiktok: '', youtube: '' },
          availability_text: data.availability_text || '',
          reachability: data.reachability || '',
          last_active: data.last_active || '',
          hide_last_active: data.hide_last_active || false,
          what_i_do: data.what_i_do || '',
          passions: data.passions || '',
          communication_style: data.communication_style || '',
          languages: Array.isArray(data.languages) ? data.languages : (typeof data.languages === 'string' ? data.languages.split(',').map(l => l.trim()).filter(Boolean) : []),
          fun_fact: data.fun_fact || '',
        });
        setPreviewUrl(data.avatar_url || null);
        setCoverPreview(data.cover_url || null);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };
    fetchProfile();
  }, []);

  useEffect(() => {
    if (mode === 'edit' || mode === 'view') {
      const fetchGallery = async () => {
        try {
          const token = JSON.parse(localStorage.getItem('auth'))?.token;
          const res = await fetch(`${import.meta.env.VITE_API_BASE_URL}/profile/`, {
            headers: { Authorization: `Bearer ${token}` },
          });
          if (!res.ok) return;
          const data = await res.json();
          setGalleryImages(Array.isArray(data.portfolio) ? data.portfolio : []);
        } catch {}
      };
      fetchGallery();
    }
  }, [mode]);

  // 1. Remove portfolio state, useEffect, and all handlers (handlePortfolioUpload, handlePortfolioReorder, handlePortfolioDelete).
  // 2. Remove all Portfolio section JSX from both view and edit modes.
  // 3. Remove all drag-and-drop (dnd-kit) imports and SortableImage component.
  // 4. Remove all portfolio upload logic and handlers.

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name.startsWith('social_links.')) {
      const key = name.split('.')[1];
      setFormData((prev) => ({
        ...prev,
        social_links: {
          ...prev.social_links,
          [key]: value,
        },
      }));
      return;
    }
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        alert('Please select a valid image file');
        return;
      }
      if (file.size > 5 * 1024 * 1024) {
        alert('File size should be less than 5MB');
        return;
      }
      setProfileImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target.result);
      };
      reader.readAsDataURL(file);
      // Upload to backend
      try {
        const token = JSON.parse(localStorage.getItem('auth'))?.token;
        const form = new FormData();
        form.append('avatar', file);
        const res = await fetch(`${import.meta.env.VITE_API_BASE_URL}/profile/avatar`, {
          method: 'POST',
          headers: { Authorization: `Bearer ${token}` },
          body: form,
        });
        if (!res.ok) throw new Error('Failed to upload avatar');
        const data = await res.json();
        setFormData(prev => ({ ...prev, avatar_url: data.avatar_url }));
      } catch (err) {
        setError('Avatar upload failed');
      }
    }
  };

  const handleCameraClick = () => {
    fileInputRef.current?.click();
  };

  const removeImage = () => {
    setProfileImage(null);
    setPreviewUrl(null);
    setFormData(prev => ({ ...prev, avatar_url: '' }));
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Cover photo upload logic
  const handleCoverUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        alert('Please select a valid image file');
        return;
      }
      if (file.size > 10 * 1024 * 1024) {
        alert('File size should be less than 10MB');
        return;
      }
      const reader = new FileReader();
      reader.onload = (e) => {
        setCoverPreview(e.target.result);
      };
      reader.readAsDataURL(file);
      // Upload to backend
      try {
        const token = JSON.parse(localStorage.getItem('auth'))?.token;
        const form = new FormData();
        form.append('cover', file);
        const res = await fetch(`${import.meta.env.VITE_API_BASE_URL}/profile/cover`, {
          method: 'POST',
          headers: { Authorization: `Bearer ${token}` },
          body: form,
        });
        if (!res.ok) throw new Error('Failed to upload cover photo');
        const data = await res.json();
        setFormData((prev) => ({ ...prev, cover_url: data.cover_url }));
      } catch (err) {
        setError('Cover photo upload failed');
      }
    }
  };
  const handleCoverClick = () => {
    coverInputRef.current?.click();
  };
  const removeCover = () => {
    setCoverPreview(null);
    setFormData((prev) => ({ ...prev, cover_url: '' }));
    if (coverInputRef.current) {
      coverInputRef.current.value = '';
    }
  };

  // Handler for uploading images
  // 1. Remove portfolio state, useEffect, and all handlers (handlePortfolioUpload, handlePortfolioReorder, handlePortfolioDelete).
  // 2. Remove all Portfolio section JSX from both view and edit modes.
  // 3. Remove all drag-and-drop (dnd-kit) imports and SortableImage component.
  // 4. Remove all portfolio upload logic and handlers.

  // Toast helper
  const showToast = (message, type = 'success') => {
    setToast({ message, type });
    setTimeout(() => setToast({ message: '', type: '' }), 3000);
  };

  // On save, return to view mode
  const handleSubmit = async (e) => {
    e.preventDefault();
    // Remove validation
    setLoading(true);
    setError('');
    try {
      const token = JSON.parse(localStorage.getItem('auth'))?.token;
      const res = await fetch(`${import.meta.env.VITE_API_BASE_URL}/profile/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(formData),
      });
      if (!res.ok) throw new Error('Failed to update profile');
      showToast('✅ Profile saved successfully', 'success');
      setMode('view');
    } catch (err) {
      setError(err.message);
      showToast(err.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  const saveGalleryToBackend = async () => {
    try {
      const token = JSON.parse(localStorage.getItem('auth'))?.token;
      await fetch(`${import.meta.env.VITE_API_BASE_URL}/profile/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ portfolio: galleryImages }),
      });
      showToast('Gallery saved!', 'success');
    } catch (err) {
      setError('Failed to save gallery');
      showToast('Failed to save gallery', 'error');
    }
  };

  // Replace getProfileCompletion with the new version that returns items with completed status
  function getProfileCompletion(formData, galleryImages) {
    const items = [
      {
        label: "Add a profile picture",
        completed: !!formData.avatar_url,
      },
      {
        label: "Add your name",
        completed: !!formData.display_name,
      },
      {
        label: "Add a short bio",
        completed: !!formData.bio,
      },
      {
        label: "Link at least one social media account",
        completed:
          formData.social_links &&
          (formData.social_links.instagram ||
            formData.social_links.tiktok ||
            formData.social_links.youtube),
      },
      {
        label: "Add at least one gallery image",
        completed: galleryImages && galleryImages.length > 0,
      },
    ];
    const percent = Math.round(
      (items.filter((item) => item.completed).length / items.length) * 100
    );
    return { percent, items };
  }

  // View mode: read-only profile card
  if (mode === 'view') {
    const { percent, items } = getProfileCompletion(formData, galleryImages);
    const missingItems = items.filter(item => !item.completed);
    return (
      <>
        <Navbar />
        <div className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-800 w-full py-8 px-2">
          <div className="w-full rounded-3xl shadow-2xl bg-gradient-to-br from-[#16213e] to-[#1f2a48] p-0 md:p-0 relative">
            {/* Profile Photo - centered and overlapping card */}
            <div className="flex flex-col items-center -mt-16">
              <div className="w-32 h-32 rounded-full border-4 border-white shadow-lg bg-slate-700 flex items-center justify-center overflow-hidden relative z-10">
                {formData.avatar_url ? (
                  <img
                    src={formData.avatar_url.startsWith('http') ? formData.avatar_url : `${import.meta.env.VITE_API_BASE_URL}/${formData.avatar_url.replace(/^\/+/,'')}`}
                    alt="Avatar"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <User className="w-16 h-16 text-slate-300" />
                )}
              </div>
            </div>
            {/* Profile Info - centered */}
            <div className="flex flex-col items-center mt-4 mb-2 px-4">
              <h1 className="text-3xl font-extrabold text-blue-200 mb-1 text-center">{formData.display_name || 'Your Name'}</h1>
              <div className="text-lg font-semibold text-blue-400 mb-2 text-center">{formData.job_title || 'Model'}</div>
              <div className="text-base text-slate-200 mb-2 text-center">{formData.bio || 'Booked & busy 🧑‍💼 | Beauty & brains'}</div>
              <div className="flex flex-col items-center gap-1 mb-2">
                <div className="flex items-center gap-2 text-gray-300 text-sm">
                  <span
                    className={`inline-block w-2 h-2 rounded-full
                      ${formData.status === 'Available' ? 'bg-green-500' : ''}
                      ${formData.status === 'Unavailable' ? 'bg-red-500' : ''}
                      ${formData.status === 'Limited' ? 'bg-yellow-400' : ''}
                      animate-pulse`}
                  ></span>
                  <span>
                    {formData.status === 'Available' && 'Available'}
                    {formData.status === 'Unavailable' && 'Unavailable'}
                    {formData.status === 'Limited' && 'Limited'}
                    {!formData.status && 'Currently unavailable'}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-gray-300 text-sm">
                  <span>📍 {formData.city && formData.country ? `${formData.city}, ${formData.country}` : (formData.city || formData.country || 'Mumbai, India')}</span>
                </div>
                <div className="text-gray-300 text-sm">Working Hours / Availability: {formData.availability_text || 'Monday to Friday 6am 12am'}</div>
                <div className="text-gray-300 text-sm">Best Time to Connect: {formData.reachability || 'Friday,2pm'}</div>
              </div>
              {/* Social Icons */}
              <div className="flex items-center justify-center gap-6 mt-2 mb-4">
                {formData.social_links.instagram && (
                  <Tooltip text="View Instagram Profile">
                    <a href={formData.social_links.instagram} target="_blank" rel="noopener noreferrer" aria-label="Instagram">
                      <FaInstagram className="text-3xl text-pink-400 hover:scale-125 hover:text-pink-500 transition-transform duration-200" />
                    </a>
                  </Tooltip>
                )}
                {formData.social_links.tiktok && (
                  <Tooltip text="View TikTok Profile">
                    <a href={formData.social_links.tiktok} target="_blank" rel="noopener noreferrer" aria-label="TikTok">
                      <FaTiktok className="text-3xl text-black dark:text-white hover:scale-125 hover:text-gray-700 transition-transform duration-200" />
                    </a>
                  </Tooltip>
                )}
                {formData.social_links.youtube && (
                  <Tooltip text="View YouTube Channel">
                    <a href={formData.social_links.youtube} target="_blank" rel="noopener noreferrer" aria-label="YouTube">
                      <FaYoutube className="text-3xl text-red-500 hover:scale-125 hover:text-red-600 transition-transform duration-200" />
                    </a>
                  </Tooltip>
                )}
              </div>
              {/* Edit Profile Button */}
              <button
                className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white font-bold py-2 px-8 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 shadow-lg text-lg mb-4"
                onClick={() => setMode('edit')}
              >
                Edit Profile
              </button>
              {/* Last Active Indicator */}
              {!formData.hide_last_active && formData.last_active && (
                <div className="text-gray-400 text-xs mb-2">Last active: {formatTimeAgo(formData.last_active)}</div>
              )}
              {/* Message Button */}
            </div>
            {/* Portfolio Section */}
            <div className="px-6 pb-8">
              <h2 className="text-xl font-medium text-blue-300 mb-4 text-left">Portfolio</h2>
              <div className="flex justify-end gap-4 mb-4">
                {/* Remove Manage Gallery button */}
              </div>
              <Gallery images={galleryImages} onChange={setGalleryImages} editable={false} />
            </div>
            {/* In view mode, group under 'More About Me' */}
            <div className="bg-[#16213e] rounded-2xl shadow-2xl p-10 mb-12 border border-blue-900">
              <h2 className="text-xl font-medium text-blue-300 mb-4 text-left">More About Me</h2>
              <div className="text-blue-200 text-base mb-8">Get to know me better!</div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
                <div className="bg-blue-950/60 border border-blue-800 rounded-xl p-6 shadow flex flex-col gap-2">
                  <div className="font-medium text-blue-100 text-lg flex items-center gap-2"><Info className="w-7 h-7 text-blue-300" />What I’m passionate about</div>
                  <div className="text-white text-base leading-relaxed">{formData.passions ? formData.passions : <span className='italic text-blue-200'>No information provided.</span>}</div>
                </div>
                <div className="bg-blue-950/60 border border-blue-800 rounded-xl p-6 shadow flex flex-col gap-2">
                  <div className="font-medium text-blue-100 text-lg flex items-center gap-2"><Clock className="w-7 h-7 text-blue-300" />How I like to communicate</div>
                  <div className="text-white text-base leading-relaxed">{formData.communication_style ? formData.communication_style : <span className='italic text-blue-200'>No information provided.</span>}</div>
                </div>
                <div className="bg-blue-950/60 border border-blue-800 rounded-xl p-6 shadow flex flex-col gap-2">
                  <div className="font-medium text-blue-100 text-lg flex items-center gap-2"><MapPin className="w-7 h-7 text-blue-300" />Languages I speak</div>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {formData.languages && formData.languages.length > 0 ? (
                      formData.languages.map((lang, idx) => (
                        <span key={idx} className="inline-block bg-blue-800 text-blue-100 px-3 py-1 rounded-full text-sm font-semibold shadow">{lang}</span>
                      ))
                    ) : (
                      <span className='italic text-blue-200'>No information provided.</span>
                    )}
                  </div>
                </div>
                <div className="bg-blue-950/60 border border-blue-800 rounded-xl p-6 shadow flex flex-col gap-2">
                  <div className="font-medium text-blue-100 text-lg flex items-center gap-2"><User className="w-7 h-7 text-blue-300" />Fun Fact</div>
                  <div className="text-white text-base leading-relaxed">{formData.fun_fact ? formData.fun_fact : <span className='italic text-blue-200'>No information provided.</span>}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  } else {
    const { percent, items } = getProfileCompletion(formData, galleryImages);
    const missingItems = items.filter(item => !item.completed);
    return (
      <div className="min-h-screen bg-[#F1EFEC] w-full py-8 px-2">
        <Navbar />
        <div className="w-full p-8">
          <div className="flex flex-col md:flex-row items-center md:items-center gap-8 mb-8">
            {/* Profile Photo */}
            <div className="flex-shrink-0 w-32 h-32 rounded-full border-4 border-white overflow-hidden bg-slate-300 shadow-lg relative group cursor-pointer" onClick={handleCameraClick} title="Change profile photo">
              {previewUrl ? (
                <img
                  src={
                    previewUrl.startsWith('http')
                      ? previewUrl
                      : `${import.meta.env.VITE_API_BASE_URL}/${previewUrl.replace(/^\/+/, '')}`
                  }
                  alt="Profile Preview"
                  className="w-full h-full object-cover"
                />
              ) : (
                <User className="w-20 h-20 text-slate-400" />
              )}
              {/* Camera Icon Overlay */}
              <div className="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 flex items-center justify-center transition-opacity duration-200">
                <Camera className="w-8 h-8 text-white" />
              </div>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleImageUpload}
                accept="image/*"
                className="hidden"
              />
            </div>
            {/* Heading and Subheading */}
            <div className="flex flex-col justify-center md:items-start items-start w-full">
              <span className="text-3xl font-extrabold text-blue-900 mb-1">Editing {formData.display_name || "Your"}'s Profile</span>
              <span className="text-lg font-normal text-gray-500">{formData.job_title || 'Model'} at ModelSuite</span>
            </div>
          </div>
          {loading && <div className="text-blue-400 text-center mb-4">Loading...</div>}
          {error && <div className="text-red-400 text-center mb-4">{error}</div>}
          {/* Progress Bar (profile completion) */}
          <div className="px-6 mb-6">
            <div className="h-1 w-full bg-slate-300 rounded-full overflow-hidden">
              <div className="h-full bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-500" style={{ width: `${percent}%` }} />
            </div>
          </div>
          {missingItems.length > 0 && (
            <ul className="text-sm mt-2 space-y-1 px-6">
              {missingItems.map((item, i) => (
                <li key={i} className="flex items-center gap-2 text-red-700">
                  <span className="text-red-400" title="Missing">⚠️</span>
                  <span>{item.label}</span>
                </li>
              ))}
            </ul>
          )}
          {mode === 'edit' && (
            <div className="flex justify-center mb-8">
              <button
                type="button"
                className="px-6 py-3 bg-blue-600 text-white rounded-lg shadow font-medium text-lg hover:bg-blue-700 transition-colors"
                onClick={() => formRef.current && formRef.current.requestSubmit()}
              >
                Save
              </button>
            </div>
          )}
          <div className="mb-8 bg-gray-50 rounded-2xl shadow p-6">
            <h2 className="text-xl font-medium text-blue-300 mb-4">Personal Info</h2>
            {/* Name, Email, Phone, Country, City fields */}
            <div>
              <label className="block text-gray-800 text-sm font-medium mb-2">Full Name</label>
              <input type="text" name="display_name" value={formData.display_name} onChange={handleInputChange} placeholder="Enter your full name" className={`w-full px-4 py-3 bg-white border rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${errors.display_name ? 'border-red-500' : 'border-gray-300'}`}/>
              {errors.display_name && <div className="text-red-500 text-xs mt-1">{errors.display_name}</div>}
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-gray-800 text-sm font-medium mb-2">Email Address</label>
                <input type="email" name="email" value={formData.email} onChange={handleInputChange} placeholder="Enter your email" className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" disabled/>
              </div>
              <div>
                <label className="block text-gray-800 text-sm font-medium mb-2">Phone Number</label>
                <input type="tel" name="phone" value={formData.phone} onChange={handleInputChange} placeholder="Enter your phone number" className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"/>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-gray-800 text-sm font-medium mb-2">Country</label>
                <input type="text" name="country" value={formData.country} onChange={handleInputChange} placeholder="Enter your country" className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"/>
              </div>
              <div>
                <label className="block text-gray-800 text-sm font-medium mb-2">City</label>
                <input type="text" name="city" value={formData.city} onChange={handleInputChange} placeholder="Enter your city" className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"/>
              </div>
            </div>
            {/* In the view mode, Personal Info section, after the city/country and before the bio, add: */}
            {mode === 'view' && (
              <div className="w-full max-w-2xl mx-auto mb-2">
                <div className="font-medium text-blue-100 text-lg flex items-center gap-2 justify-center">
                  <Briefcase className="w-5 h-5 text-blue-400" />What I do at ModelSuite
                </div>
                <div className="text-blue-100 text-base leading-relaxed text-center mt-1">
                  {formData.what_i_do ? formData.what_i_do : <span className='italic text-blue-200'>No information provided.</span>}
                </div>
              </div>
            )}
            {mode === 'edit' && (
              <div className="mb-6">
                <label className="flex items-center gap-2 text-black text-base font-medium mb-2">
                  <Briefcase className="w-5 h-5 text-blue-400" />
                  What I do at ModelSuite
                </label>
                <div className="flex items-center bg-white border-2 border-gray-300 rounded-lg px-3 py-2">
                  <textarea name="what_i_do" value={formData.what_i_do} onChange={handleInputChange} placeholder="Describe your main responsibilities or how you support the team." rows={2} className="w-full bg-transparent text-black placeholder-gray-400 focus:outline-none resize-none text-base" />
                </div>
              </div>
            )}
          </div>
          <div className="mb-8 bg-gray-50 rounded-2xl shadow p-6">
            <h2 className="text-xl font-medium text-blue-300 mb-4">Job Details</h2>
            {/* Bio, Job Title, Role Description, Status, Availability, Reachability fields */}
            <div>
              <label className="block text-gray-800 text-sm font-medium mb-2">Short Bio</label>
              <textarea name="bio" value={formData.bio} onChange={handleInputChange} placeholder="Briefly introduce yourself or describe what you do" maxLength={300} className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"/>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-gray-800 text-sm font-medium mb-2">Job Title</label>
                <select name="job_title" value={formData.job_title} onChange={handleInputChange} className={`w-full px-4 py-3 bg-white border rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${errors.job_title ? 'border-red-500' : 'border-gray-300'}`}>{jobTitleOptions.map((option) => (<option key={option} value={option}>{option || 'Select job title'}</option>))}</select>
                {errors.job_title && <div className="text-red-500 text-xs mt-1">{errors.job_title}</div>}
              </div>
              <div>
                <label className="block text-gray-800 text-sm font-medium mb-2">Role Description</label>
                <input type="text" name="role_description" value={formData.role_description} onChange={handleInputChange} placeholder="Describe your role" className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"/>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-gray-800 text-sm font-medium mb-2">Status</label>
                <select name="status" value={formData.status} onChange={handleInputChange} className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"><option value="">Select status</option><option value="Available">Available</option><option value="Unavailable">Unavailable</option><option value="Limited">Limited</option></select>
              </div>
              <div>
                <label className="block text-gray-800 text-sm font-medium mb-2">Working Hours / Availability</label>
                <input type="text" name="availability_text" value={formData.availability_text || ''} onChange={handleInputChange} placeholder="e.g. Monday to Friday, 10am–6pm" className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"/>
              </div>
            </div>
            <div>
              <label className="block text-gray-800 text-sm font-medium mb-2">Best Time to Connect</label>
              <input type="text" name="reachability" value={formData.reachability || ''} onChange={handleInputChange} placeholder="When is the best time to connect with you?" className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"/>
            </div>
          </div>
          <div className="mb-8 bg-gray-50 rounded-2xl shadow p-6">
            <h2 className="text-xl font-medium text-blue-300 mb-4">Social Media</h2>
            {/* Social Media fields (Instagram, TikTok, YouTube) */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="text-gray-800 text-sm font-medium mb-2 flex items-center gap-2"><FaInstagram className="text-pink-400" /> Instagram</label>
                <input type="url" name="social_links.instagram" value={formData.social_links.instagram} onChange={handleInputChange} placeholder="Instagram profile URL" className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-pink-400 focus:border-transparent"/>
              </div>
              <div>
                <label className="text-gray-800 text-sm font-medium mb-2 flex items-center gap-2"><FaTiktok className="text-black dark:text-white" /> TikTok</label>
                <input type="url" name="social_links.tiktok" value={formData.social_links.tiktok} onChange={handleInputChange} placeholder="TikTok profile URL" className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"/>
              </div>
              <div>
                <label className="flex items-center gap-2 text-gray-800 text-sm font-medium mb-2"><FaYoutube className="text-red-500" /> YouTube</label>
                <input type="url" name="social_links.youtube" value={formData.social_links.youtube} onChange={handleInputChange} placeholder="YouTube channel URL" className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"/>
              </div>
            </div>
          </div>
          {/* Change the More About Me block to use gray background, rounded corners, and shadow */}
          <div className="mb-10 bg-white rounded-2xl shadow p-6">
            <h2 className="text-xl font-medium text-blue-300 mb-4 text-left">More About Me</h2>
            <div className="mb-6">
              <label className="flex items-center gap-2 text-black text-base font-medium mb-2">
                <Info className="w-5 h-5 text-blue-400" />
                What I’m passionate about
              </label>
              <div className="flex items-center bg-white border-2 border-gray-300 rounded-lg px-3 py-2">
                <input type="text" name="passions" value={formData.passions} onChange={handleInputChange} placeholder="e.g. I love storytelling through photography, helping new talent grow" className="w-full bg-transparent text-black placeholder-gray-400 focus:outline-none text-base" />
              </div>
            </div>
            <div className="mb-6">
              <label className="flex items-center gap-2 text-black text-base font-medium mb-2">
                <Clock className="w-5 h-5 text-blue-400" />
                How I like to communicate
              </label>
              <div className="flex items-center bg-white border-2 border-gray-300 rounded-lg px-3 py-2">
                <input type="text" name="communication_style" value={formData.communication_style} onChange={handleInputChange} placeholder="e.g. Best via email for official topics, quick things on Slack" className="w-full bg-transparent text-black placeholder-gray-400 focus:outline-none text-base" />
              </div>
            </div>
            <div className="mb-6">
              <label className="flex items-center gap-2 text-black text-base font-medium mb-2">
                <MapPin className="w-5 h-5 text-blue-400" />
                Languages I speak
              </label>
              <div className="flex items-center bg-white border-2 border-gray-300 rounded-lg px-3 py-2">
                <input type="text" name="languages" value={formData.languages.join(', ')} onChange={e => setFormData(prev => ({ ...prev, languages: e.target.value.split(',').map(l => l.trim()).filter(Boolean) }))} placeholder="e.g. English, Spanish, German" className="w-full bg-transparent text-black placeholder-gray-400 focus:outline-none text-base" />
              </div>
            </div>
            <div className="mb-2">
              <label className="flex items-center gap-2 text-black text-base font-medium mb-2">
                <User className="w-5 h-5 text-blue-400" />
                Fun Fact
              </label>
              <div className="flex items-center bg-white border-2 border-gray-300 rounded-lg px-3 py-2">
                <input type="text" name="fun_fact" value={formData.fun_fact} onChange={handleInputChange} placeholder="e.g. I used to be a touring musician. I’m obsessed with organizing Notion boards." className="w-full bg-transparent text-black placeholder-gray-400 focus:outline-none text-base" />
              </div>
            </div>
          </div>
        </div>
        <form ref={formRef} onSubmit={handleSubmit}>
          {/* The form's onSubmit handler is now handled by the button */}
        </form>
      </div>
    );
  }
}

function formatAvailability(availability) {
  if (!availability) return '';
  // Example: { monday: ["am", "pm"], tuesday: ["am"], ... }
  const days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];
  return days
    .map(day => {
      const slots = availability[day];
      if (slots && slots.length) {
        return `${day.charAt(0).toUpperCase() + day.slice(1)}: ${slots.join(", ")}`;
      }
      return null;
    })
    .filter(Boolean)
    .join(" | ");
}

function isCurrentlyAvailable(availabilityText) {
  if (!availabilityText) return false;
  // Example: 'Monday to Friday, 10am–6pm'
  // Parse days and hours
  const match = availabilityText.match(/([A-Za-z]+) to ([A-Za-z]+), (\d{1,2})(am|pm)–(\d{1,2})(am|pm)/);
  if (!match) return false;
  const days = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];
  const now = new Date();
  const today = days[now.getDay()];
  const [_, startDay, endDay, startHour, startPeriod, endHour, endPeriod] = match;
  // Check if today is in range
  const startIdx = days.indexOf(startDay);
  const endIdx = days.indexOf(endDay);
  const todayIdx = days.indexOf(today);
  let inDayRange = false;
  if (startIdx <= endIdx) {
    inDayRange = todayIdx >= startIdx && todayIdx <= endIdx;
  } else {
    // e.g., Friday to Monday
    inDayRange = todayIdx >= startIdx || todayIdx <= endIdx;
  }
  if (!inDayRange) return false;
  // Check time
  let sh = parseInt(startHour, 10);
  let eh = parseInt(endHour, 10);
  if (startPeriod === 'pm' && sh !== 12) sh += 12;
  if (startPeriod === 'am' && sh === 12) sh = 0;
  if (endPeriod === 'pm' && eh !== 12) eh += 12;
  if (endPeriod === 'am' && eh === 12) eh = 0;
  const nowHour = now.getHours();
  return nowHour >= sh && nowHour < eh;
}

function formatTimeAgo(dateString) {
  if (!dateString) return '';
  const date = new Date(dateString);
  const now = new Date();
  const diff = Math.floor((now - date) / 1000);
  if (diff < 60) return `${diff} seconds ago`;
  if (diff < 3600) return `${Math.floor(diff / 60)} minutes ago`;
  if (diff < 86400) return `${Math.floor(diff / 3600)} hours ago`;
  return `${Math.floor(diff / 86400)} days ago`;
}
