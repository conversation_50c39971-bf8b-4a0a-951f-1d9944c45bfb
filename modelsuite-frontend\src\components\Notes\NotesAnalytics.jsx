import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Area,
  AreaChart
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  FileText,
  Clock,
  Star,
  Tag,
  Calendar,
  Users,
  Activity,
  BarChart3,
  Pie<PERSON><PERSON> as PieChartIcon,
  Download,
  Filter
} from 'lucide-react';
import axios from 'axios';

/**
 * Advanced Notes Analytics Dashboard
 * Provides comprehensive insights into notes usage, trends, and performance
 */
const NotesAnalytics = ({ modelId, agencyId, userRole = 'agency' }) => {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [timeRange, setTimeRange] = useState('30d'); // 7d, 30d, 90d, 1y
  const [selectedMetric, setSelectedMetric] = useState('notes_created');

  const baseURL = import.meta.env.VITE_API_BASE_URL;
  const token = JSON.parse(localStorage.getItem('auth'))?.token;

  // Fetch analytics data
  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const response = await axios.get(
        `${baseURL}/notes/analytics/${modelId}?timeRange=${timeRange}`,
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      if (response.data.success) {
        setAnalyticsData(response.data.data);
      }
    } catch (err) {
      setError('Failed to fetch analytics data');
      console.error('Error fetching analytics:', err);
      // Mock data for demonstration
      setAnalyticsData(generateMockAnalytics());
    } finally {
      setLoading(false);
    }
  };

  // Generate mock analytics data for demonstration
  const generateMockAnalytics = () => {
    const dates = [];
    const today = new Date();
    for (let i = 29; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      dates.push({
        date: date.toISOString().split('T')[0],
        notes_created: Math.floor(Math.random() * 10) + 1,
        notes_updated: Math.floor(Math.random() * 8) + 1,
        notes_viewed: Math.floor(Math.random() * 25) + 5,
        reminders_set: Math.floor(Math.random() * 5) + 1
      });
    }

    return {
      overview: {
        totalNotes: 156,
        activeNotes: 142,
        pinnedNotes: 23,
        notesWithReminders: 45,
        notesWithAttachments: 67,
        avgNotesPerDay: 5.2,
        mostActiveDay: 'Monday',
        growthRate: 12.5
      },
      timeline: dates,
      categories: [
        { name: 'Personal', value: 45, color: '#8B5CF6' },
        { name: 'Work', value: 67, color: '#06B6D4' },
        { name: 'Ideas', value: 23, color: '#10B981' },
        { name: 'Tasks', value: 21, color: '#F59E0B' }
      ],
      priorities: [
        { name: 'High', value: 34, color: '#EF4444' },
        { name: 'Medium', value: 78, color: '#F59E0B' },
        { name: 'Low', value: 44, color: '#10B981' }
      ],
      weeklyActivity: [
        { day: 'Mon', notes: 12, reminders: 3 },
        { day: 'Tue', notes: 8, reminders: 2 },
        { day: 'Wed', notes: 15, reminders: 5 },
        { day: 'Thu', notes: 10, reminders: 1 },
        { day: 'Fri', notes: 18, reminders: 4 },
        { day: 'Sat', notes: 6, reminders: 1 },
        { day: 'Sun', notes: 4, reminders: 0 }
      ],
      topTags: [
        { tag: 'important', count: 45 },
        { tag: 'meeting', count: 32 },
        { tag: 'idea', count: 28 },
        { tag: 'todo', count: 25 },
        { tag: 'project', count: 22 }
      ]
    };
  };

  useEffect(() => {
    if (modelId) {
      fetchAnalytics();
    }
  }, [modelId, timeRange]);

  // Export analytics data
  const exportData = (format = 'csv') => {
    if (!analyticsData) return;

    const data = {
      overview: analyticsData.overview,
      timeline: analyticsData.timeline,
      exportedAt: new Date().toISOString()
    };

    const dataStr = format === 'json' 
      ? JSON.stringify(data, null, 2)
      : convertToCSV(analyticsData.timeline);
    
    const dataBlob = new Blob([dataStr], { type: format === 'json' ? 'application/json' : 'text/csv' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `notes-analytics-${new Date().toISOString().split('T')[0]}.${format}`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const convertToCSV = (data) => {
    const headers = Object.keys(data[0]).join(',');
    const rows = data.map(row => Object.values(row).join(','));
    return [headers, ...rows].join('\n');
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-700 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-700 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 text-center">
        <div className="bg-red-900/20 border border-red-700 rounded-lg p-4">
          <p className="text-red-300">{error}</p>
          <button 
            onClick={fetchAnalytics}
            className="mt-2 text-sm text-red-400 hover:text-red-300 underline"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  const { overview, timeline, categories, priorities, weeklyActivity, topTags } = analyticsData;

  return (
    <div className="p-6 space-y-6 bg-gray-900 text-white">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <BarChart3 className="text-blue-400" size={24} />
            Notes Analytics
          </h2>
          <p className="text-gray-400 text-sm mt-1">
            Comprehensive insights into your notes usage and patterns
          </p>
        </div>
        <div className="flex items-center gap-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          <button
            onClick={() => exportData('csv')}
            className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
          >
            <Download size={16} />
            Export CSV
          </button>
          <button
            onClick={() => exportData('json')}
            className="bg-gray-700 hover:bg-gray-600 px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
          >
            <Download size={16} />
            Export JSON
          </button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Notes</p>
              <p className="text-2xl font-bold">{overview.totalNotes}</p>
            </div>
            <FileText className="text-blue-400" size={24} />
          </div>
          <div className="flex items-center mt-2">
            <TrendingUp className="text-green-400" size={16} />
            <span className="text-green-400 text-sm ml-1">+{overview.growthRate}%</span>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Notes</p>
              <p className="text-2xl font-bold">{overview.activeNotes}</p>
            </div>
            <Activity className="text-green-400" size={24} />
          </div>
          <div className="flex items-center mt-2">
            <span className="text-gray-400 text-sm">
              {((overview.activeNotes / overview.totalNotes) * 100).toFixed(1)}% of total
            </span>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Avg. Notes/Day</p>
              <p className="text-2xl font-bold">{overview.avgNotesPerDay}</p>
            </div>
            <Calendar className="text-purple-400" size={24} />
          </div>
          <div className="flex items-center mt-2">
            <span className="text-gray-400 text-sm">
              Most active: {overview.mostActiveDay}
            </span>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">With Reminders</p>
              <p className="text-2xl font-bold">{overview.notesWithReminders}</p>
            </div>
            <Clock className="text-yellow-400" size={24} />
          </div>
          <div className="flex items-center mt-2">
            <span className="text-gray-400 text-sm">
              {((overview.notesWithReminders / overview.totalNotes) * 100).toFixed(1)}% of total
            </span>
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Timeline Chart */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <TrendingUp className="text-blue-400" size={20} />
            Notes Activity Timeline
          </h3>
          <ResponsiveContainer width="100%" height={250}>
            <AreaChart data={timeline}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis 
                dataKey="date" 
                stroke="#9CA3AF"
                fontSize={12}
                tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
              />
              <YAxis stroke="#9CA3AF" fontSize={12} />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: '#1F2937', 
                  border: '1px solid #374151',
                  borderRadius: '8px',
                  color: '#F9FAFB'
                }}
                labelFormatter={(value) => new Date(value).toLocaleDateString()}
              />
              <Area 
                type="monotone" 
                dataKey={selectedMetric} 
                stroke="#3B82F6" 
                fill="#3B82F6" 
                fillOpacity={0.3}
              />
            </AreaChart>
          </ResponsiveContainer>
          <div className="flex gap-2 mt-4">
            {['notes_created', 'notes_updated', 'notes_viewed', 'reminders_set'].map((metric) => (
              <button
                key={metric}
                onClick={() => setSelectedMetric(metric)}
                className={`px-3 py-1 rounded text-xs transition-colors ${
                  selectedMetric === metric 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {metric.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </button>
            ))}
          </div>
        </div>

        {/* Weekly Activity */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <BarChart3 className="text-green-400" size={20} />
            Weekly Activity
          </h3>
          <ResponsiveContainer width="100%" height={250}>
            <BarChart data={weeklyActivity}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis dataKey="day" stroke="#9CA3AF" fontSize={12} />
              <YAxis stroke="#9CA3AF" fontSize={12} />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: '#1F2937', 
                  border: '1px solid #374151',
                  borderRadius: '8px',
                  color: '#F9FAFB'
                }}
              />
              <Bar dataKey="notes" fill="#10B981" radius={[4, 4, 0, 0]} />
              <Bar dataKey="reminders" fill="#F59E0B" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Categories Distribution */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <PieChartIcon className="text-purple-400" size={20} />
            Categories Distribution
          </h3>
          <ResponsiveContainer width="100%" height={250}>
            <PieChart>
              <Pie
                data={categories}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={5}
                dataKey="value"
              >
                {categories.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: '#1F2937', 
                  border: '1px solid #374151',
                  borderRadius: '8px',
                  color: '#F9FAFB'
                }}
              />
            </PieChart>
          </ResponsiveContainer>
          <div className="grid grid-cols-2 gap-2 mt-4">
            {categories.map((category, index) => (
              <div key={index} className="flex items-center gap-2">
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: category.color }}
                ></div>
                <span className="text-sm text-gray-300">
                  {category.name} ({category.value})
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Top Tags */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Tag className="text-yellow-400" size={20} />
            Most Used Tags
          </h3>
          <div className="space-y-3">
            {topTags.map((tag, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="bg-blue-600 text-white px-2 py-1 rounded text-xs">
                    #{tag.tag}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-20 bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full" 
                      style={{ width: `${(tag.count / topTags[0].count) * 100}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-300 w-8 text-right">
                    {tag.count}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Insights Section */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <TrendingUp className="text-green-400" size={20} />
          Key Insights
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-blue-900/20 border border-blue-700 rounded-lg p-4">
            <h4 className="font-medium text-blue-300 mb-2">Most Productive Day</h4>
            <p className="text-sm text-gray-300">
              You create the most notes on {overview.mostActiveDay}s with an average of {Math.max(...weeklyActivity.map(d => d.notes))} notes.
            </p>
          </div>
          <div className="bg-green-900/20 border border-green-700 rounded-lg p-4">
            <h4 className="font-medium text-green-300 mb-2">Growth Trend</h4>
            <p className="text-sm text-gray-300">
              Your note creation has increased by {overview.growthRate}% compared to the previous period.
            </p>
          </div>
          <div className="bg-purple-900/20 border border-purple-700 rounded-lg p-4">
            <h4 className="font-medium text-purple-300 mb-2">Organization Level</h4>
            <p className="text-sm text-gray-300">
              {((overview.notesWithReminders / overview.totalNotes) * 100).toFixed(0)}% of your notes have reminders, showing good organization habits.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotesAnalytics;