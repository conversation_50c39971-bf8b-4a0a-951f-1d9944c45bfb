import React, { useState, useEffect } from "react";
import {
  Search,
  Plus,
  Filter,
  Pin,
  Edit3,
  Trash2,
  Eye,
  Calendar,
  Tag,
  AlertCircle,
  FileText,
  Clock,
  Star,
  MoreVertical,
  Bell,
  Paperclip,
  BarChart3,
  CheckSquare,
  Square,
  Copy,
  Share2,
  EyeOff,
  Archive,
  Download,
  Upload,
  Save,
  X,
  Info,
} from "lucide-react";
import axios from "axios";
import { toast } from "sonner";
import NoteCard from "./NoteCard";
import CreateNoteModal from "./CreateNoteModal";
import EditNoteModal from "./EditNoteModal";
import FilterPanel from "./FilterPanel";
import NoteStats from "./NoteStats";
import ReminderSystem from "./ReminderSystem";
import FileAttachment from "./FileAttachment";
import NotesAnalytics from "./NotesAnalytics";
import SystemOverview from "./SystemOverview";

/**
 * Main Notes Manager component that handles all note operations
 * Integrates with the existing backend API and follows project design patterns
 */
const NotesManager = ({ modelId, agencyId, userRole = "agency" }) => {
  const [notes, setNotes] = useState([]);
  const [filteredNotes, setFilteredNotes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  const [selectedNote, setSelectedNote] = useState(null);
  const [showNoteDetails, setShowNoteDetails] = useState(false);
  const [selectedNoteForDetails, setSelectedNoteForDetails] = useState(null);
  const [noteReminders, setNoteReminders] = useState([]);
  const [noteAttachments, setNoteAttachments] = useState([]);
  const [stats, setStats] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [currentView, setCurrentView] = useState("notes"); // notes, analytics
  const [selectedNotes, setSelectedNotes] = useState(new Set());
  const [bulkActionMode, setBulkActionMode] = useState(false);
  const [showSystemOverview, setShowSystemOverview] = useState(false);
  const [filters, setFilters] = useState({
    category: "",
    priority: "",
    status: "active",
    tags: [],
    isPinned: null,
  });

  const baseURL =
    import.meta.env.VITE_API_BASE_URL || "http://localhost:5000/api/v1";
  const token = JSON.parse(localStorage.getItem("auth"))?.token;

  // Fetch notes with filters and pagination
  const fetchNotes = async (page = 1, searchQuery = "", filterParams = {}) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "10",
        ...(searchQuery && { search: searchQuery }),
        ...filterParams,
      });

      const response = await axios.get(
        `${baseURL}/notes/model/${modelId}?${params}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (response.data.success) {
        setNotes(response.data.data.notes || []);
        setFilteredNotes(response.data.data.notes || []);
        setCurrentPage(response.data.data.currentPage || 1);
        setTotalPages(response.data.data.totalPages || 1);
      }
    } catch (err) {
      setError("Failed to fetch notes");
      console.error("Error fetching notes:", err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch note statistics
  const fetchStats = async () => {
    try {
      const response = await axios.get(
        `${baseURL}/notes/model/${modelId}/stats`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (response.data.success) {
        setStats(response.data.data);
      }
    } catch (err) {
      console.error("Error fetching stats:", err);
    }
  };

  // Handle note creation
  const handleCreateNote = async (noteData) => {
    try {
      const response = await axios.post(
        `${baseURL}/notes`,
        { ...noteData, modelId, agencyId },
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (response.data.success) {
        setShowCreateModal(false);
        fetchNotes(currentPage, searchTerm, filters);
        fetchStats();
      }
    } catch (err) {
      console.error("Error creating note:", err);
      throw err;
    }
  };

  // Handle note details view
  const handleViewNoteDetails = async (note) => {
    setSelectedNoteForDetails(note);
    setShowNoteDetails(true);

    // Fetch reminders and attachments for the note
    try {
      const [remindersResponse, attachmentsResponse] = await Promise.all([
        axios.get(`${baseURL}/notes/${note._id}/reminders`, {
          headers: { Authorization: `Bearer ${token}` },
        }),
        axios.get(`${baseURL}/notes/${note._id}/attachments`, {
          headers: { Authorization: `Bearer ${token}` },
        }),
      ]);

      if (remindersResponse.data.success) {
        setNoteReminders(remindersResponse.data.data);
      }

      if (attachmentsResponse.data.success) {
        setNoteAttachments(attachmentsResponse.data.data);
      }
    } catch (error) {
      console.error("Error fetching note details:", error);
    }
  };

  // Handle reminder updates
  const handleReminderUpdate = (updatedReminders) => {
    setNoteReminders(updatedReminders);
  };

  // Handle attachment updates
  const handleAttachmentUpdate = (updatedAttachments) => {
    setNoteAttachments(updatedAttachments);
  };

  // Handle note update
  const handleUpdateNote = async (noteId, updateData) => {
    try {
      const response = await axios.patch(
        `${baseURL}/notes/${noteId}`,
        updateData,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (response.data.success) {
        setShowEditModal(false);
        setSelectedNote(null);
        fetchNotes(currentPage, searchTerm, filters);
        fetchStats();
      }
    } catch (err) {
      console.error("Error updating note:", err);
      throw err;
    }
  };

  // Handle note deletion
  const handleDeleteNote = async (noteId) => {
    if (!window.confirm("Are you sure you want to delete this note?")) return;

    try {
      await axios.delete(`${baseURL}/notes/${noteId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      fetchNotes(currentPage, searchTerm, filters);
      fetchStats();
    } catch (err) {
      console.error("Error deleting note:", err);
    }
  };

  // Handle pin/unpin note
  const handleTogglePin = async (noteId) => {
    try {
      await axios.patch(
        `${baseURL}/notes/${noteId}/pin`,
        {},
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      fetchNotes(currentPage, searchTerm, filters);
    } catch (err) {
      console.error("Error toggling pin:", err);
    }
  };

  // Handle search
  const handleSearch = (query) => {
    setSearchTerm(query);
    setCurrentPage(1);
    fetchNotes(1, query, filters);
  };

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    setCurrentPage(1);
    fetchNotes(1, searchTerm, newFilters);
  };

  // Handle pagination
  const handlePageChange = (page) => {
    setCurrentPage(page);
    fetchNotes(page, searchTerm, filters);
  };

  // Bulk operations
  const handleSelectNote = (noteId) => {
    const newSelected = new Set(selectedNotes);
    if (newSelected.has(noteId)) {
      newSelected.delete(noteId);
    } else {
      newSelected.add(noteId);
    }
    setSelectedNotes(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedNotes.size === filteredNotes.length) {
      setSelectedNotes(new Set());
    } else {
      setSelectedNotes(new Set(filteredNotes.map((note) => note._id)));
    }
  };

  const bulkDeleteNotes = async () => {
    if (selectedNotes.size === 0) return;

    if (
      !window.confirm(
        `Are you sure you want to delete ${selectedNotes.size} notes?`
      )
    ) {
      return;
    }

    try {
      await Promise.all(
        Array.from(selectedNotes).map((noteId) =>
          axios.delete(`${baseURL}/notes/${noteId}`, {
            headers: { Authorization: `Bearer ${token}` },
          })
        )
      );

      setNotes(notes.filter((note) => !selectedNotes.has(note._id)));
      setSelectedNotes(new Set());
      setBulkActionMode(false);
      toast.success(`${selectedNotes.size} notes deleted successfully`);
      fetchStats();
    } catch (err) {
      console.error("Error deleting notes:", err);
      toast.error("Failed to delete some notes");
    }
  };

  const bulkPinNotes = async () => {
    if (selectedNotes.size === 0) return;

    try {
      await Promise.all(
        Array.from(selectedNotes).map((noteId) =>
          axios.patch(
            `${baseURL}/notes/${noteId}`,
            { isPinned: true },
            { headers: { Authorization: `Bearer ${token}` } }
          )
        )
      );

      setNotes(
        notes.map((note) =>
          selectedNotes.has(note._id) ? { ...note, isPinned: true } : note
        )
      );
      setSelectedNotes(new Set());
      setBulkActionMode(false);
      toast.success(`${selectedNotes.size} notes pinned successfully`);
    } catch (err) {
      console.error("Error pinning notes:", err);
      toast.error("Failed to pin some notes");
    }
  };

  // Export notes functionality
  const exportNotes = async (format = "json") => {
    try {
      const notesToExport =
        selectedNotes.size > 0
          ? notes.filter((note) => selectedNotes.has(note._id))
          : filteredNotes;

      const exportData = {
        notes: notesToExport,
        exportedAt: new Date().toISOString(),
        totalNotes: notesToExport.length,
        modelId: modelId,
      };

      const dataStr =
        format === "json"
          ? JSON.stringify(exportData, null, 2)
          : convertNotesToCSV(notesToExport);

      const dataBlob = new Blob([dataStr], {
        type: format === "json" ? "application/json" : "text/csv",
      });

      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `notes-export-${
        new Date().toISOString().split("T")[0]
      }.${format}`;
      link.click();
      URL.revokeObjectURL(url);

      toast.success(
        `${notesToExport.length} notes exported as ${format.toUpperCase()}`
      );
    } catch (err) {
      console.error("Error exporting notes:", err);
      toast.error("Failed to export notes");
    }
  };

  const convertNotesToCSV = (notesData) => {
    const headers = [
      "Title",
      "Content",
      "Category",
      "Tags",
      "Priority",
      "Created At",
      "Updated At",
      "Is Pinned",
    ];
    const rows = notesData.map((note) => [
      note.title || "",
      note.content || "",
      note.category || "",
      (note.tags || []).join("; "),
      note.priority || "",
      note.createdAt || "",
      note.updatedAt || "",
      note.isPinned ? "Yes" : "No",
    ]);

    return [headers, ...rows]
      .map((row) =>
        row.map((field) => `"${String(field).replace(/"/g, '""')}"`).join(",")
      )
      .join("\n");
  };

  // Initial data fetch
  useEffect(() => {
    if (modelId && token) {
      fetchNotes();
      fetchStats();
    }
  }, [modelId, token]);

  if (loading && notes.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Stats */}
      {stats && <NoteStats stats={stats} />}

      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-white flex items-center gap-2">
            <FileText className="text-blue-400" size={24} />
            Notes Manager
          </h2>
          <p className="text-gray-400 text-sm mt-1">
            Manage your notes, reminders, and attachments
          </p>
        </div>
        <div className="flex items-center gap-3">
          {/* View Toggle */}
          <div className="bg-gray-800 rounded-lg p-1 flex">
            <button
              onClick={() => setCurrentView("notes")}
              className={`px-3 py-2 rounded-md text-sm transition-colors ${
                currentView === "notes"
                  ? "bg-blue-600 text-white"
                  : "text-gray-400 hover:text-white"
              }`}
            >
              <FileText size={16} className="inline mr-1" />
              Notes
            </button>
            <button
              onClick={() => setCurrentView("analytics")}
              className={`px-3 py-2 rounded-md text-sm transition-colors ${
                currentView === "analytics"
                  ? "bg-blue-600 text-white"
                  : "text-gray-400 hover:text-white"
              }`}
            >
              <BarChart3 size={16} className="inline mr-1" />
              Analytics
            </button>
          </div>

          {/* System Overview Button */}
          <button
            onClick={() => setShowSystemOverview(true)}
            className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-all duration-200"
            title="View complete system overview"
          >
            <Info size={16} />
            System Overview
          </button>

          {currentView === "notes" && (
            <>
              {/* Bulk Actions */}
              {bulkActionMode && (
                <div className="flex items-center gap-2 bg-gray-800 rounded-lg px-3 py-2">
                  <span className="text-sm text-gray-300">
                    {selectedNotes.size} selected
                  </span>
                  <button
                    onClick={bulkPinNotes}
                    className="text-yellow-400 hover:text-yellow-300 p-1"
                    title="Pin selected"
                  >
                    <Pin size={16} />
                  </button>
                  <button
                    onClick={bulkDeleteNotes}
                    className="text-red-400 hover:text-red-300 p-1"
                    title="Delete selected"
                  >
                    <Trash2 size={16} />
                  </button>
                  <button
                    onClick={() => {
                      setBulkActionMode(false);
                      setSelectedNotes(new Set());
                    }}
                    className="text-gray-400 hover:text-gray-300 p-1"
                    title="Cancel"
                  >
                    <X size={16} />
                  </button>
                </div>
              )}

              {/* Main Content */}
              <div className="relative">
                <button
                  onClick={() => exportNotes("json")}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
                  title="Export as JSON"
                >
                  <Download size={16} />
                  Export
                </button>
              </div>

              <button
                onClick={() => setBulkActionMode(!bulkActionMode)}
                className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
                  bulkActionMode
                    ? "bg-orange-600 text-white"
                    : "bg-gray-700 hover:bg-gray-600 text-gray-300"
                }`}
              >
                <CheckSquare size={16} />
                {bulkActionMode ? "Exit Bulk" : "Bulk Actions"}
              </button>

              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <Plus size={16} />
                Create Note
              </button>

              <button
                onClick={() => setShowFilterPanel(!showFilterPanel)}
                className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
                  showFilterPanel
                    ? "bg-blue-600 text-white"
                    : "bg-gray-700 hover:bg-gray-600 text-gray-300"
                }`}
              >
                <Filter size={16} />
                Filters
              </button>
            </>
          )}
        </div>
      </div>

      {/* Search and Actions Bar */}
      {currentView === "notes" && (
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search notes..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {bulkActionMode && (
            <div className="flex items-center gap-2">
              <button
                onClick={handleSelectAll}
                className="inline-flex items-center gap-2 px-3 py-2 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors"
              >
                {selectedNotes.size === filteredNotes.length ? (
                  <CheckSquare className="h-4 w-4" />
                ) : (
                  <Square className="h-4 w-4" />
                )}
                Select All
              </button>
            </div>
          )}
        </div>
      )}

      {/* Filter Panel */}
      {showFilterPanel && (
        <FilterPanel
          filters={filters}
          onFilterChange={handleFilterChange}
          onClose={() => setShowFilterPanel(false)}
        />
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-900/20 border border-red-700 rounded-lg p-4 flex items-center gap-2 text-red-300">
          <AlertCircle className="h-5 w-5" />
          {error}
        </div>
      )}

      {/* Main Content */}
      {currentView === "analytics" ? (
        <NotesAnalytics
          modelId={modelId}
          agencyId={agencyId}
          userRole={userRole}
        />
      ) : (
        <>
          {/* Notes Grid */}
          {filteredNotes.length > 0 ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredNotes.map((note) => (
                <div key={note._id} className="relative">
                  {bulkActionMode && (
                    <div className="absolute top-2 left-2 z-10">
                      <button
                        onClick={() => handleSelectNote(note._id)}
                        className={`p-1 rounded transition-colors ${
                          selectedNotes.has(note._id)
                            ? "bg-blue-600 text-white"
                            : "bg-gray-700 text-gray-300 hover:bg-gray-600"
                        }`}
                      >
                        {selectedNotes.has(note._id) ? (
                          <CheckSquare size={16} />
                        ) : (
                          <Square size={16} />
                        )}
                      </button>
                    </div>
                  )}
                  <NoteCard
                    note={note}
                    userRole={userRole}
                    onEdit={(note) => {
                      setSelectedNote(note);
                      setShowEditModal(true);
                    }}
                    onDelete={handleDeleteNote}
                    onTogglePin={handleTogglePin}
                    onView={handleViewNoteDetails}
                    isSelected={selectedNotes.has(note._id)}
                    bulkMode={bulkActionMode}
                  />
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-gray-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-300 mb-2">
                {searchTerm ||
                Object.values(filters).some((f) => f && f.length > 0)
                  ? "No notes found"
                  : "No notes yet"}
              </h3>
              <p className="text-gray-500">
                {searchTerm ||
                Object.values(filters).some((f) => f && f.length > 0)
                  ? "Try adjusting your search or filters"
                  : userRole === "agency"
                  ? "Create your first note to get started"
                  : "No notes have been shared with you yet"}
              </p>
            </div>
          )}
        </>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center gap-2 mt-6">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="px-3 py-2 bg-gray-800 text-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-700 transition-colors"
          >
            Previous
          </button>

          <span className="text-gray-400">
            Page {currentPage} of {totalPages}
          </span>

          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="px-3 py-2 bg-gray-800 text-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-700 transition-colors"
          >
            Next
          </button>
        </div>
      )}

      {/* Modals */}
      {showCreateModal && (
        <CreateNoteModal
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateNote}
        />
      )}

      {showEditModal && selectedNote && (
        <EditNoteModal
          note={selectedNote}
          onClose={() => {
            setShowEditModal(false);
            setSelectedNote(null);
          }}
          onSubmit={(updateData) =>
            handleUpdateNote(selectedNote._id, updateData)
          }
        />
      )}

      {/* Note Details Modal */}
      {showNoteDetails && selectedNoteForDetails && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-white">Note Details</h2>
              <button
                onClick={() => {
                  setShowNoteDetails(false);
                  setSelectedNoteForDetails(null);
                }}
                className="text-gray-400 hover:text-white"
              >
                ×
              </button>
            </div>

            <div className="space-y-6">
              {/* Note Content */}
              <div className="bg-gray-700 rounded-lg p-4">
                <h3 className="text-lg font-medium text-white mb-2">
                  {selectedNoteForDetails.title}
                </h3>
                <p className="text-gray-300">
                  {selectedNoteForDetails.content}
                </p>
              </div>

              {/* Reminders */}
              <ReminderSystem
                noteId={selectedNoteForDetails._id}
                reminders={noteReminders}
                onUpdate={handleReminderUpdate}
              />

              {/* Attachments */}
              <FileAttachment
                noteId={selectedNoteForDetails._id}
                attachments={noteAttachments}
                onUpdate={handleAttachmentUpdate}
              />
            </div>
          </div>
        </div>
      )}

      {/* System Overview Modal */}
      {showSystemOverview && (
        <SystemOverview
          modelId={modelId}
          agencyId={agencyId}
          userRole={userRole}
          onClose={() => setShowSystemOverview(false)}
        />
      )}
    </div>
  );
};

export default NotesManager;
