import React, { useEffect, useState } from "react";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "./ChatWindow";
import { Check, CheckCheckIcon, Loader, Loader2, Loader2Icon, LoaderIcon, Plus } from "lucide-react";
import { useDispatch, useSelector } from "react-redux";
import axios from "axios";
import { addNewDmconversation } from "../globalstate/dmSlice";
import socket from "../utils/socket";
import { FileImage } from "lucide-react";
import { FileVideo } from "lucide-react";
import { FileAudio } from "lucide-react";
import { File } from "lucide-react";
import { FileText } from "lucide-react";

// Dummy data for demonstration
const groups = [];
const channels = [];

export default function AgencyMessanger({ modeinfo }) {
  const user = JSON.parse(localStorage.getItem("auth"))?.user;
  const token = JSON.parse(localStorage.getItem("auth"))?.token;

  const [activeTab, setActiveTab] = useState("groups");
  const [search, setSearch] = useState("");
  const [activeChat, setActiveChat] = useState(null);
  const [isCreatingDM, setIsCreatingDm] = useState(false);
  const dispatch = useDispatch();
  const baseURL = import.meta.env.VITE_API_BASE_URL;
  const dmConversations = useSelector((state) => state.dm.conversations);
  // Filter to get only conversations where members include the target user
  const existingDm = dmConversations?.filter((convo) =>
    convo.members.some((member) => member.userId === modeinfo._id)
  )[0];
  const dmUnread =
    existingDm?.messages?.filter(
      (m) => m.status !== "seen" && m.senderId !== user._id
    ).length || 0;

  console.log(existingDm, "existingdm");

  const isDmLoading = useSelector((state) => state.dm.isLoading);
  // Filter conversations by tab and search
  const conversations = activeTab === "groups" ? groups : channels;
  const filteredConvos = conversations.filter((c) =>
    c.name.toLowerCase().includes(search.toLowerCase())
  );

  const createNewDm = async (targetUserId) => {
    setIsCreatingDm(true);
    try {
      const res = await axios.post(
        `${baseURL}/messanger/dm/createNewDm`,
        { targetUserId },
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      // return the new conversation object
      dispatch(addNewDmconversation([res.data.conversation, user._id]));
      socket.emit("dm:addNewConversation", {
        dmConvo: res.data.conversation,
        targetUserId,
      });
      console.log(res.data.conversation);
      setIsCreatingDm(false);
    } catch (err) {
      console.error("error while adding new dm", err);
      setIsCreatingDm(false);
    }
  };

  function renderLastMessage(c) {
    const lastMessage = c.messages[c.messages.length - 1];
    const attachments = lastMessage.attachments || [];
    const text = lastMessage.text?.trim();

    if (attachments.length === 0) {
      return text || "Attachment";
    }

    const counts = {
      image: 0,
      video: 0,
      audio: 0,
      document: 0,
      other: 0,
    };

    attachments.forEach((att) => {
      if (att.type === "image") counts.image++;
      else if (att.type === "video") counts.video++;
      else if (att.type === "audio") counts.audio++;
      else if (
        ["pdf", "doc", "docx", "text", "txt"].includes(att.fileExtension)
      ) {
        counts.document++;
      } else {
        counts.other++;
      }
    });

    const parts = [];

    if (counts.image > 0) {
      parts.push(
        <span key="img" className="flex items-center gap-1">
          <FileImage className="w-4 h-4 text-muted-foreground" />
          {counts.image} {counts.image === 1 ? "image" : "images"}
        </span>
      );
    }

    if (counts.video > 0) {
      parts.push(
        <span key="vid" className="flex items-center gap-1">
          <FileVideo className="w-4 h-4 text-muted-foreground" />
          {counts.video} {counts.video === 1 ? "video" : "videos"}
        </span>
      );
    }

    if (counts.audio > 0) {
      parts.push(
        <span key="aud" className="flex items-center gap-1">
          <FileAudio className="w-4 h-4 text-muted-foreground" />
          {counts.audio} {counts.audio === 1 ? "audio" : "audios"}
        </span>
      );
    }

    if (counts.document > 0) {
      parts.push(
        <span key="doc" className="flex items-center gap-1">
          <FileText className="w-4 h-4 text-muted-foreground" />
          {counts.document} {counts.document === 1 ? "document" : "documents"}
        </span>
      );
    }

    if (counts.other > 0) {
      parts.push(
        <span key="other" className="flex items-center gap-1">
          <File className="w-4 h-4 text-muted-foreground" />
          {counts.other} {counts.other === 1 ? "file" : "files"}
        </span>
      );
    }

    // Combine all into a wrapper span with spacing
    return <span className="flex flex-wrap gap-x-2 gap-y-1">{parts}</span>;
  }

  useEffect(() => {}, [existingDm, modeinfo]);

  // Demo: open ChatWindow with dummy data
  if (activeChat) {
    return (
      <ChatWindow
        convoId={activeChat.convoId}
        type={activeChat.type}
        onBack={() => setActiveChat(null)}
      />
    );
  }

  return (
    <>
      {/* DM Card - always on top, separated, premium look */}
      {isDmLoading ? (
        // Loading effect
        <div className="flex items-center gap-4 px-8 py-5 mb-2 bg-gray-700/40 animate-pulse  shadow rounded-2xl">
          <div className="w-14 h-14 rounded-full bg-gray-600"></div>
          <div className="flex flex-col flex-1">
            <div className="h-4 bg-gray-600 rounded w-1/2 mb-2"></div>
            <div className="h-3 bg-gray-600 rounded w-1/3"></div>
          </div>
        </div>
      ) : existingDm ? (
        // Existing conversation card
        <div
          className="flex rounded-2xl items-center gap-4 px-8 py-5 mb-2 bg-gradient-to-r from-[#7746cc] via-[#4c4eb6] to-[#3c7ef7] shadow-xl border border-[#312e81] hover:shadow-2xl transition-shadow duration-300 cursor-pointer group relative"
          onClick={() =>
            setActiveChat({ convoId: existingDm.convoId, type: "dm" })
          }
        >
          <div className="w-14 h-14 relative rounded-full bg-gradient-to-br from-[#a5b4fc] to-[#6366f1] flex items-center justify-center text-white font-bold text-2xl uppercase shadow-lg border-4 border-[#23272f] group-hover:border-[#a5b4fc]">
            {modeinfo.fullName?.charAt(0)}
            {existingDm?.members[0]?.userId !== user._id &&
              existingDm?.members[0]?.status.isOnline && (
                <span className="w-3 h-3 rounded-full border bg-green-500 absolute inline-block bottom-0 right-0"></span>
              )}
            {existingDm?.members[1]?.userId !== user._id &&
              existingDm?.members[1]?.status.isOnline && (
                <span className="w-3 h-3 rounded-full border bg-green-500 absolute inline-block bottom-0 right-0"></span>
              )}
          </div>
          <div className="flex flex-col">
            <div className="text-lg font-semibold tracking-wide text-white drop-shadow group-hover:text-[#e0e7ff]">
              <span className="capitalize text-[#e0e7ff]">
                {modeinfo?.fullName}
              </span>
            </div>
            <div className="text-xs text-[#c7d2fe] mt-1">
              {existingDm?.messages[existingDm.messages.length - 1]?.senderId == user._id &&
              existingDm?.messages[existingDm.messages.length - 1]?.status === "sent" ? (
                <Check className="inline shrink-0 mr-1" size={18} />
              ) : existingDm?.messages[existingDm.messages.length - 1]?.senderId == user._id &&
                existingDm?.messages[existingDm.messages.length - 1]?.status === "seen" ? (
                <CheckCheckIcon className="inline shrink-0 mr-1" size={18} />
              ) : null}
              {renderLastMessage(existingDm)}
            </div>
          </div>
          <span className="absolute right-6 top-1/2 -translate-y-1/2 flex items-center gap-2">
            <span className="bg-[#a5b4fc]/20 px-3 py-1 rounded-full text-xs text-[#e0e7ff] font-semibold shadow group-hover:bg-[#a5b4fc]/40 transition">
              DM
            </span>
            {dmUnread > 0 && (
              <span className="bg-red-500 border-2 text-white rounded-full text-xs px-2 min-w-[20px] text-center font-bold ml-2">
                {dmUnread}
              </span>
            )}
          </span>
        </div>
      ) : (
        // No existing conversation → create prompt card
        <div
          className="flex rounded-2xl items-center gap-4 px-8 py-5 mb-2 bg-gradient-to-r from-[#4c4eb6] via-[#3c7ef7] to-[#4c4eb6] shadow-xl border border-[#312e81] hover:shadow-2xl transition-shadow duration-300 cursor-pointer group relative"
          onClick={() => createNewDm(modeinfo._id)}
        >
          <div className="w-14 h-14 rounded-full bg-gradient-to-br from-[#a5b4fc] to-[#6366f1] flex items-center justify-center text-white font-bold text-2xl uppercase shadow-lg border-4 border-[#23272f] group-hover:border-[#a5b4fc]">
            {modeinfo.fullName?.charAt(0)}
          </div>
          <div className="flex flex-col">
            <div className="text-lg font-semibold tracking-wide text-white drop-shadow group-hover:text-[#e0e7ff]">
              Create direct chat with{" "}
              <span className="capitalize text-[#e0e7ff]">
                {modeinfo.fullName}
              </span>
            </div>
            <div className="text-xs text-[#c7d2fe] mt-1">Agency to Model</div>
          </div>
          <span className="absolute right-6 top-1/2 -translate-y-1/2 flex items-center gap-2">
            <span className="bg-green-500/20 px-3 py-1 rounded-full text-xs text-[#e0e7ff] font-semibold shadow group-hover:bg-green-500/40 transition">
              {isCreatingDM ? <Loader className="spin" /> : "NEW"}
            </span>
          </span>
        </div>
      )}
      <div className="bg-gradient-to-b from-[#181a20] via-[#23272f] to-[#312e81] text-white font-sans flex flex-col max-h-[90vh] min-h-[90vh] rounded-3xl overflow-hidden shadow-2xl">
        {/* Main container for tabs, search, and conversations */}
        <div className="flex flex-col flex-1 bg-gradient-to-b from-[#23272f] via-[#181a20] to-[#23272f] rounded-2xl    shadow-lg border border-[#23272f]">
          {/* Tabs */}
          <div className="flex border-b border-[#23272f] bg-gradient-to-r from-[#23272f] via-[#312e81] to-[#23272f] rounded-t-2xl">
            <div
              onClick={() => setActiveTab("groups")}
              className={`flex-1 px-6 py-3 cursor-pointer flex items-center justify-center font-semibold text-base relative transition-all duration-200 rounded-t-2xl
              ${
                activeTab === "groups"
                  ? "bg-gradient-to-r from-[#6366f1] to-[#4f8cff] text-white shadow"
                  : "text-[#a5b4fc] hover:bg-[#232b38]"
              }
            `}
            >
              Groups
              {
                <span className="ml-2 bg-[#2563eb] text-white rounded-full text-xs px-2 min-w-[20px] text-center font-bold"></span>
              }
            </div>
            <div
              onClick={() => setActiveTab("channels")}
              className={`flex-1 px-6 py-3 cursor-pointer flex items-center justify-center font-semibold text-base relative transition-all duration-200 rounded-t-2xl
              ${
                activeTab === "channels"
                  ? "bg-gradient-to-r from-[#a78bfa] to-[#6366f1] text-white shadow"
                  : "text-[#a5b4fc] hover:bg-[#232b38]"
              }
            `}
            >
              Compaign channels
              {
                <span className="ml-2 bg-[#2563eb] text-white rounded-full text-xs px-2 min-w-[20px] text-center font-bold">
                  {}
                </span>
              }
            </div>
          </div>

          {/* Search Bar + Add Button */}
          <div className="flex items-center px-6 py-4 bg-gradient-to-r from-[#181a20] via-[#2a3446] to-[#1e2229] border-b border-[#3f3f3f]">
            <input
              type="text"
              placeholder={`search in ${activeTab}`}
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="flex-1 px-4 py-2 rounded-lg border border-[#323741] bg-[#23272f] text-white text-base outline-none focus:ring-2 focus:ring-[#6366f1] transition-all"
            />
            <button className="ml-4 w-10 h-10 rounded-full bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center shadow-lg hover:scale-105 transition-transform border-4 border-[#23272f]">
              <Plus className="w-6 h-6 text-white" />
            </button>
          </div>

          {/* Conversation List */}
          <div
            className="flex-1 overflow-y-auto  custom-scrollbar"
            style={{ minHeight: 0, maxHeight: 350 }}
          >
            {filteredConvos.length === 0 && (
              <div className="text-[#bbb] p-8 text-center">
                No conversations found.
              </div>
            )}
            {filteredConvos.map((c) => (
              <div
                key={c.id}
                onClick={() =>
                  setActiveChat({ convoId: "c.convoId", type: "c.type" })
                }
                className="flex items-center px-6 py-4 border-b border-[#333] bg-[#23272f] cursor-pointer transition-colors hover:bg-[#232b38] relative"
              >
                <div
                  className={`text-2xl w-11 h-11 rounded-full flex items-center justify-center mr-4 border-2 ${
                    c.unread ? "border-[#4f8cff]" : "border-transparent"
                  } bg-[#222]`}
                >
                  {c.avatar}
                </div>
                <div className="flex-1">
                  <div className="font-bold text-white text-base mb-0.5">
                    {c.name}
                  </div>
                  <div className="text-[#bbb] text-sm truncate">
                    {c.lastMessage}
                  </div>
                </div>
                {c.unread > 0 && (
                  <span className="bg-[#2563eb] text-white rounded-full text-xs px-2 ml-3 min-w-[20px] text-center font-bold">
                    {c.unread}
                  </span>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
}
