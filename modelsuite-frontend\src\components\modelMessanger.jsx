import React, { useState } from "react";
import Chat<PERSON><PERSON>ow from "./ChatWindow";
import { useSelector } from "react-redux";
import { Check, CheckCheckIcon } from "lucide-react";
import { formateTime } from "../utils/functions";

// Dummy data for demonstration
const conversations = [];

const tabOptions = [
  { key: "all", label: "All" },
  { key: "dm", label: "DMs" },
  { key: "group", label: "Groups" },
  { key: "channel", label: "Channels" },
];

function getUnreadCount(type) {
  if (type === "all") return conversations.reduce((a, c) => a + c.unread, 0);
  return conversations
    .filter((c) => c.type === type)
    .reduce((a, c) => a + c.unread, 0);
}

function renderLastMessage(c) {
  const lastMessage = c.messages[c.messages.length - 1];
  const attachments = lastMessage?.attachments || [];
  const text = lastMessage?.text?.trim();

  if (attachments.length === 0) {
    return text || "Attachment";
  }

  const counts = {
    image: 0,
    video: 0,
    audio: 0,
    document: 0,
    other: 0,
  };

  attachments.forEach((att) => {
    if (att.type === "image") counts.image++;
    else if (att.type === "video") counts.video++;
    else if (att.type === "audio") counts.audio++;
    else if (["pdf", "doc", "docx", "text", "txt"].includes(att.fileExtension)) {
      counts.document++;
    } else {
      counts.other++;
    }
  });

  const parts = [];

  if (counts.image > 0) {
    parts.push(
      <span key="img" className="flex items-center gap-1">
        <FileImage className="w-4 h-4 text-muted-foreground" />
        {counts.image} {counts.image === 1 ? "image" : "images"}
      </span>
    );
  }

  if (counts.video > 0) {
    parts.push(
      <span key="vid" className="flex items-center gap-1">
        <FileVideo className="w-4 h-4 text-muted-foreground" />
        {counts.video} {counts.video === 1 ? "video" : "videos"}
      </span>
    );
  }

  if (counts.audio > 0) {
    parts.push(
      <span key="aud" className="flex items-center gap-1">
        <FileAudio className="w-4 h-4 text-muted-foreground" />
        {counts.audio} {counts.audio === 1 ? "audio" : "audios"}
      </span>
    );
  }

  if (counts.document > 0) {
    parts.push(
      <span key="doc" className="flex items-center gap-1">
        <FileText className="w-4 h-4 text-muted-foreground" />
        {counts.document} {counts.document === 1 ? "document" : "documents"}
      </span>
    );
  }

  if (counts.other > 0) {
    parts.push(
      <span key="other" className="flex items-center gap-1">
        <File className="w-4 h-4 text-muted-foreground" />
        {counts.other} {counts.other === 1 ? "file" : "files"}
      </span>
    );
  }

  // Combine all into a wrapper span with spacing
  return <span className="flex flex-wrap gap-x-2 gap-y-1">{parts}</span>;
}

// Model layout (current design, moved from default export)
function ModelMessanger() {
  const user = JSON.parse(localStorage.getItem("auth")).user;
  const [selectedTab, setSelectedTab] = useState("all");
  const [search, setSearch] = useState("");
  const [activeChat, setActiveChat] = useState(null);

  const dmConversations = useSelector((state) => state.dm.conversations);
  console.log(dmConversations);
  const filteredConvos = dmConversations?.filter((c) => {
    const matchesTab = selectedTab === "all" || c.type == selectedTab;
    console.log(c.type);
    console.log(selectedTab);
    const opponentName =
      c?.members[0]?.userId !== user._id
        ? c.members[0]?.name
        : c.members[1]?.name;
    const matchesSearch = opponentName
      ?.toLowerCase()
      .includes(search.toLowerCase());
    return matchesTab && matchesSearch;
  });

  if (activeChat) {
    return (
      <ChatWindow
        convoId={activeChat.convoId}
        type={activeChat.type}
        onBack={() => setActiveChat(null)}
      />
    );
  }

  return (
    <div className="bg-gradient-to-b from-[#1e293b] via-[#1e40af] to-[#181a20] text-white font-sans flex flex-col max-h-[90vh] min-h-[90vh] rounded-3xl overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-[#23272f] via-[#1e40af] to-[#23272f] px-6 py-5 border-b border-[#333] font-bold text-2xl tracking-wide sticky top-0 z-20">
        Messaging
      </div>

      {/* Tabs/Menu */}
      <div className="flex border-b border-[#333] bg-gradient-to-r from-[#23272f] via-[#1e3a8a] to-[#23272f] sticky top-[61px] z-10">
        {tabOptions.map((tab) => (
          <div
            key={tab.key}
            onClick={() => setSelectedTab(tab.key)}
            className={`px-6 py-3 cursor-pointer flex items-center font-medium text-base relative
                            ${
                              selectedTab === tab.key
                                ? "text-[#4f8cff] border-b-2 border-[#4f8cff] font-bold"
                                : "text-white border-b-2 border-transparent"
                            }
                        `}
          >
            {tab.label}
            {getUnreadCount(tab.key) > 0 && (
              <span className="bg-[#2563eb] text-white rounded-full text-xs px-2 ml-2 min-w-[20px] text-center font-bold">
                {getUnreadCount(tab.key)}
              </span>
            )}
          </div>
        ))}
      </div>

      {/* Search Bar */}
      <div className="px-6 py-4 bg-gradient-to-r from-[#181a20] via-[#1e293b] to-[#23272f] border-b border-[#333] sticky top-[109px] z-10">
        <input
          type="text"
          placeholder="Search conversations..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="w-full px-4 py-2 rounded-lg border border-[#333] bg-[#23272f] text-white text-base outline-none"
        />
      </div>

      {/* Conversation List */}
      <div className="flex-1 overflow-y-auto custom-scrollbar bg-gradient-to-b from-[#181a20] via-[#1e293b] to-[#1e40af]">
        {filteredConvos.length === 0 && (
          <div className="text-[#bbb] p-8 text-center">
            No conversations found.
          </div>
        )}
        {filteredConvos.map((c) => (
          <div
            key={c.convoId}
            onClick={() => setActiveChat({ type: "dm", convoId: c.convoId })}
            className="flex items-center w-full px-6 py-4 border-b border-[#333] bg-[#23272f] cursor-pointer transition-colors hover:bg-[#232b38] relative"
          >
            <div className="relative mr-4">
              <div
                className={`overflow-hidden min-w-11 relative aspect-square rounded-full flex items-center justify-center  border-2 ${
                  c.unread ? "border-[#4f8cff]" : "border-transparent"
                } bg-[#222]`}
              >
                <img
                  src={c?.members[0]?.avatar}
                  alt="logo"
                  className="w-11 aspect-square object-cover"
                />
              </div>
           {c?.members[0]?.userId !== user._id &&
                c?.members[0]?.status.isOnline && (
                  <span className="w-3 h-3 rounded-full border bg-green-500 absolute inline-block bottom-0 right-0"></span>
                )}
              {c?.members[1]?.userId !== user._id &&
                c?.members[1]?.status.isOnline && (
                  <span className="w-3 h-3 rounded-full border bg-green-500 absolute inline-block bottom-0 right-0"></span>
                )}
            </div>

            <div className="flex flex-col flex-grow min-w-0">
              {" "}
              {/* KEY PART */}
              <div className="flex justify-between items-center w-full text-base font-bold text-white mb-0.5">
                <span className="truncate block">
                  {c?.members[0]?.userId !== user._id
                    ? c.members[0]?.name
                    : c.members[1]?.name}
                </span>
                <span className="text-xs text-gray-400 ml-2 shrink-0 whitespace-nowrap">
                  {c.messages.length > 0
                    ? formateTime(c?.messages[c.messages.length - 1]?.createdAt)
                    : formateTime(c.createdAt)}
                </span>
              </div>
              <div className="flex justify-between text-[#bbb] text-sm whitespace-nowrap w-full">
                <div className="truncate flex items-center min-w-0">
                  {c?.messages[c.messages.length - 1]?.senderId == user._id &&
                  c?.messages[c.messages.length - 1]?.status === "sent" ? (
                    <Check className="inline shrink-0 mr-1" size={18} />
                  ) : c?.messages[c.messages.length - 1]?.senderId ==
                      user._id &&
                    c?.messages[c.messages.length - 1]?.status === "seen" ? (
                    <CheckCheckIcon
                      className="inline shrink-0 mr-1"
                      size={18}
                    />
                  ) : null}
                  <span className="truncate">
                    <p>{renderLastMessage(c)}</p>
                  </span>
                </div>

                {c?.messages?.filter(
                  (m) => m.status !== "seen" && m.senderId !== user._id
                ).length > 0 && (
                  <span className="bg-[#2563eb] text-white rounded-full text-xs px-2 ml-3 min-w-[20px] text-center font-bold shrink-0 self-end">
                    {c.messages.filter((m) => m.status !== "seen").length}
                  </span>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default ModelMessanger;
