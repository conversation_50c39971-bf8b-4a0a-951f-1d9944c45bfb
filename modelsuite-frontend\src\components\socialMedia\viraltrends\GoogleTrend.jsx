import React, { useState } from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON>, Bar } from 'recharts';
import { Search, TrendingUp, Globe, Calendar, Settings, Loader2 } from 'lucide-react';
import axios from 'axios';

const GoogleTrend = () => {
  const [searchQuery, setSearchQuery] = useState(''); // Replace with your actual SerpAPI key
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState(null);
  const [error, setError] = useState('');
  const [searchParams, setSearchParams] = useState({
    geo: 'IN',
    date: 'today 12-m',
    data_type: 'TIMESERIES',
    hl: 'en',
    tz: '330'
  });
  const [showSettings, setShowSettings] = useState(false);

  const dataTypes = [
    { value: 'TIMESERIES', label: 'Interest over time' },
    { value: 'GEO_MAP', label: 'Compared breakdown by region(more than one query)' },
    { value: 'GEO_MAP_0', label: 'Interest by region(One query only)' },
    { value: 'RELATED_TOPICS', label: 'Related topics(one query only)' },
    { value: 'RELATED_QUERIES', label: 'Related queries(one query only)' }
  ];

  const dateOptions = [
    { value: 'now 1-H', label: 'Past hour' },
    { value: 'now 4-H', label: 'Past 4 hours' },
    { value: 'now 1-d', label: 'Past day' },
    { value: 'now 7-d', label: 'Past 7 days' },
    { value: 'today 1-m', label: 'Past 30 days' },
    { value: 'today 3-m', label: 'Past 90 days' },
    { value: 'today 12-m', label: 'Past 12 months' },
    { value: 'today 5-y', label: 'Past 5 years' },
    { value: 'all', label: '2004 - present' }
  ];

  const geoOptions = [
    { value: '', label: 'Worldwide' },
    { value: 'IN', label: 'India' },
    { value: 'US', label: 'United States' },
    { value: 'GB', label: 'United Kingdom' },
    { value: 'CA', label: 'Canada' },
    { value: 'AU', label: 'Australia' },
    { value: 'DE', label: 'Germany' },
    { value: 'JP', label: 'Japan' },
    { value: 'BR', label: 'Brazil' }
  ];

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setError('Please enter a search query');
      return;
    }

    setLoading(true);
    setError('');
    setData(null);

    try {
      const params = new URLSearchParams({
        engine: 'google_trends',
        q: searchQuery,
        ...searchParams,
        output: 'json',
        csv: 'false',
        include_low_search_volume: 'true',
        no_cache: 'true',
        async: 'false'
      });

      const response = await axios.get(`${import.meta.env.VITE_API_BASE_URL}/Trending/trend?${params}`);
      const result = response.data;

      if (result.error) {
        setError(result.error);
      } else {
        setData(result);
      }
      console.log(result, JSON.stringify(result));
    } catch (err) {
      setError('Failed to fetch data. Please check your API key and try again.', err);
      console.log(err);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  // const renderTimeSeriesChart = () => {
  //   if (!data?.interest_over_time?.timeline_data) return null;

  //   const chartData = data.interest_over_time.timeline_data.map(item => {
  //     const dataPoint = { date: item.date, timestamp: item.timestamp };
  //     item.values.forEach(value => {
  //       dataPoint[value.query] = value.extracted_value;
  //     });
  //     return dataPoint;
  //   });

  //   const queries = data.interest_over_time.timeline_data[0]?.values.map(v => v.query) || [];
  //   const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00'];

  //   return (
  //     <div className="bg-white rounded-lg shadow p-6 mb-6">
  //       <h3 className="text-xl font-semibold mb-4 flex items-center">
  //         <TrendingUp className="mr-2" size={20} />
  //         Interest Over Time
  //       </h3>
  //       <ResponsiveContainer width="100%" height={400}>
  //         <LineChart data={chartData}>
  //           <CartesianGrid strokeDasharray="3 3" />
  //           <XAxis
  //             dataKey="date"
  //             angle={-45}
  //             textAnchor="end"
  //             height={60}
  //             interval={Math.floor(chartData.length / 10)}
  //           />
  //           <YAxis />
  //           <Tooltip />
  //           <Legend />
  //           {queries.map((query, index) => (
  //             <Line
  //               key={query}
  //               type="monotone"
  //               dataKey={query}
  //               stroke={colors[index % colors.length]}
  //               strokeWidth={2}
  //               dot={{ r: 2 }}
  //             />
  //           ))}
  //         </LineChart>
  //       </ResponsiveContainer>

  //       {data.interest_over_time.averages && (
  //         <div className="mt-4">
  //           <h4 className="text-lg font-medium mb-2">Average Interest Scores:</h4>
  //           <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
  //             {data.interest_over_time.averages.map((avg, index) => (
  //               <div key={avg.query} className="bg-gray-50 rounded-lg p-3 text-center">
  //                 <div className="text-sm text-gray-600">{avg.query}</div>
  //                 <div className="text-2xl font-bold" style={{ color: colors[index % colors.length] }}>
  //                   {avg.value}
  //                 </div>
  //               </div>
  //             ))}
  //           </div>
  //         </div>
  //       )}
  //     </div>
  //   );
  // };

  const renderTimeSeriesChart = () => {
    if (!data?.interest_over_time?.timeline_data) return null;

    // Transform timeline data
    const chartData = data.interest_over_time.timeline_data
      .filter(item => item.values && item.values.length > 0) // Ensure data is not empty
      .map(item => {
        const dataPoint = {
          date: item.date,
          timestamp: item.timestamp,
        };
        item.values.forEach(value => {
          dataPoint[value.query] = value.extracted_value;
        });
        return dataPoint;
      });

    // Extract query names
    const queries = data.interest_over_time.timeline_data[0]?.values.map(v => v.query) || [];

    // Define colors for lines
    const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00'];

    return (
      <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl shadow-lg border border-gray-700 p-6 mb-6">
        <h3 className="text-xl font-semibold mb-4 flex items-center text-white">
          <TrendingUp className="mr-2" size={20} />
          Interest Over Time
        </h3>

        <ResponsiveContainer width="100%" height={400}>
          <LineChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="date"
              angle={-45}
              textAnchor="end"
              height={60}
              interval={Math.max(1, Math.floor(chartData.length / 12))}
            />
            <YAxis />
            <Tooltip />
            <Legend />
            {queries.map((query, index) => (
              <Line
                key={query}
                type="monotone"
                dataKey={query}
                stroke={colors[index % colors.length]}
                strokeWidth={2}
                dot={{ r: 2 }}
              />
            ))}
          </LineChart>
        </ResponsiveContainer>

        {data.interest_over_time.averages && (
          <div className="mt-6">
            <h4 className="text-lg font-medium mb-3">Average Interest Scores:</h4>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
              {data.interest_over_time.averages.map((avg, index) => (
                <div
                  key={avg.query}
                  className="bg-gray-50 rounded-lg p-4 text-center shadow-sm"
                >
                  <div className="text-sm text-gray-600">{avg.query}</div>
                  <div
                    className="text-2xl font-bold"
                    style={{ color: colors[index % colors.length] }}
                  >
                    {avg.value}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderRegionBreakdown = () => {
    if (!data?.compared_breakdown_by_region) return null;

    const chartData = data.compared_breakdown_by_region.slice(0, 15).map(item => {
      const dataPoint = { location: item.location };
      item.values.forEach(value => {
        dataPoint[value.query] = value.extracted_value;
      });
      return dataPoint;
    });

    const queries = data.compared_breakdown_by_region[0]?.values.map(v => v.query) || [];
    const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00'];

    return (
      <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl shadow-lg border border-gray-700 p-6 mb-6">
        <h3 className="text-xl font-semibold mb-4 flex items-center text-white">
          <Globe className="mr-2" size={20} />
          Breakdown by Region (Top 15)
        </h3>
        <ResponsiveContainer width="100%" height={400}>
          <BarChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="location"
              angle={-45}
              textAnchor="end"
              height={80}
              interval={0}
            />
            <YAxis />
            <Tooltip />
            <Legend />
            {queries.map((query, index) => (
              <Bar
                key={query}
                dataKey={query}
                fill={colors[index % colors.length]}
              />
            ))}
          </BarChart>
        </ResponsiveContainer>
      </div>
    );
  };

  const renderInterestByRegion = () => {
    if (!data?.interest_by_region) return null;

    return (
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h3 className="text-xl font-semibold mb-4 flex text-pink-700 items-center">
          <Globe className="mr-2" size={20} />
          Interest by Region
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {data.interest_by_region.slice(0, 12).map((item) => (
            <div key={item.geo} className="bg-gray-800 rounded-lg p-4 border border-gray-700">
              <div className="flex justify-between items-center">
                <div>
                  <div className="text-black font-semibold">{item.location}</div>
                  <div className="text-sm text-gray-600">{item.geo}</div>
                </div>
                <div className="text-2xl font-bold text-pink-400">
                  {item.extracted_value}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // const renderRelatedTopics = () => {
  //   if (!data?.related_topics) return null;

  //   return (
  //     <div className="bg-white text-black rounded-lg shadow p-6 mb-6">
  //       <h3 className="text-xl font-semibold mb-4">Related Topics</h3>
  //       <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
  //         {data.related_topics.rising && (
  //           <div>
  //             <h4 className="text-lg font-medium mb-3 text-green-600">Rising Topics</h4>
  //             <div className="space-y-2">
  //               {data.related_topics.rising.map((topic, index) => (
  //                 <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100">
  //                   <div className='flex flex-col gap-1 text-slate-700 font-mono'>
  //                     <span className="text-sm"><span>Title: </span>{topic.topic.title}</span>
  //                     <span className="text-sm"><span>Type: </span>{topic.topic.type}</span>
  //                     <span className="text-sm"><span>Link: </span>{topic.link}</span>
  //                     <span className="text-sm"><span>Serapi Link: </span>{topic.serpapi_link}</span>
  //                   </div>
  //                   <span className="text-sm font-medium text-green-600">{topic.value}</span>
  //                 </div>
  //               ))}
  //             </div>
  //           </div>
  //         )}
  //         {data.related_topics.top && (
  //           <div>
  //             <h4 className="text-lg font-medium mb-3 text-pink-600">Top Topics</h4>
  //             <div className="space-y-2">
  //               {data.related_topics.top.slice(0, 10).map((topic, index) => (
  //                 <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100">
  //                   <div className='flex flex-col gap-1 text-slate-700 font-mono'>
  //                     <span className="text-sm"><span>Title: </span>{topic.topic.title}</span>
  //                     <span className="text-sm"><span>Type: </span>{topic.topic.type}</span>
  //                     <span className="text-sm"><span>Link: </span>{topic.link}</span>
  //                     <span className="text-sm"><span>Serapi Link: </span>{topic.serpapi_link}</span>
  //                   </div>
  //                   <span className="text-sm font-medium text-pink-600">{topic.value}</span>
  //                 </div>
  //               ))}
  //             </div>
  //           </div>
  //         )}
  //       </div>
  //     </div>
  //   );
  // };
const renderRelatedTopics = () => {
  if (!data?.related_topics) return null;

  return (
    <div className="bg-white text-black rounded-lg shadow p-6 mb-6">
      <h3 className="text-xl font-semibold mb-4">Related Topics</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {data.related_topics.rising && (
          <div className="overflow-x-auto">
            <h4 className="text-lg font-medium mb-3 text-green-600">Rising Topics</h4>
            <div className="space-y-2">
              {data.related_topics.rising.slice(0,10).map((topic, index) => (
                <div
                  key={index}
                  className="flex flex-col sm:flex-row sm:justify-between sm:items-center py-3 border-b border-gray-100 gap-3"
                >
                  <div className="flex flex-col gap-1 text-slate-700 font-mono break-words">
                    <span className="text-sm"><strong>Title:</strong> {topic.topic.title}</span>
                    <span className="text-sm"><strong>Type:</strong> {topic.topic.type}</span>
                    <span className="text-sm whitespace-normal break-all">
                      <strong>Link:</strong> <a href={topic.link} className="text-blue-600 underline">{topic.link}</a>
                    </span>
                    <span className="text-sm whitespace-normal break-all">
                      <strong>Serapi Link:</strong> <a href={topic.serpapi_link} className="text-blue-600 underline">{topic.serpapi_link}</a>
                    </span>
                  </div>
                  <span className="text-sm font-medium text-green-600 shrink-0">
                    {topic.value}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {data.related_topics.top && (
          <div className="overflow-x-auto">
            <h4 className="text-lg font-medium mb-3 text-pink-600">Top Topics</h4>
            <div className="space-y-2">
              {data.related_topics.top.slice(0, 10).map((topic, index) => (
                <div
                  key={index}
                  className="flex flex-col sm:flex-row sm:justify-between sm:items-center py-3 border-b border-gray-100 gap-3"
                >
                  <div className="flex flex-col gap-1 text-slate-700 font-mono break-words">
                    <span className="text-sm"><strong>Title:</strong> {topic.topic.title}</span>
                    <span className="text-sm"><strong>Type:</strong> {topic.topic.type}</span>
                    <span className="text-sm whitespace-normal break-all">
                      <strong>Link:</strong> <a href={topic.link} className="text-blue-600 underline">{topic.link}</a>
                    </span>
                    <span className="text-sm whitespace-normal break-all">
                      <strong>Serapi Link:</strong> <a href={topic.serpapi_link} className="text-blue-600 underline">{topic.serpapi_link}</a>
                    </span>
                  </div>
                  <span className="text-sm font-medium text-pink-600 shrink-0">
                    {topic.value}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

  const renderRelatedQueries = () => {
    if (!data?.related_queries) return null;

    return (
      <div className="bg-white rounded-lg shadow p-6 text-black mb-6">
        <h3 className="text-xl font-semibold mb-4">Related Queries</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {data.related_queries.rising && (
            <div>
              <h4 className="text-lg  mb-3 text-green-600 font-semibold">Rising Queries</h4>
              <div className="space-y-2">
                {data.related_queries.rising.slice(0, 10).map((query, index) => (
                  <div key={index} className="flex justify-between items-center font-mono font-semibold capitalize py-2 border-b border-gray-100">
                    <span className="text-sm">{query.query}</span>
                    <span className="text-sm font-medium text-green-600">{query.value}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
          {data.related_queries.top && (
            <div>
              <h4 className="text-lg  mb-3 text-pink-600 font-semibold">Top Queries</h4>
              <div className="space-y-2">
                {data.related_queries.top.slice(0, 10).map((query, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b font-mono font-semibold capitalize border-gray-100">
                    <span className="text-sm">{query.query}</span>
                    <span className="text-sm font-semibold text-pink-600">{query.value}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-950 to-slate-900">
      <div className="max-w-6xl mx-auto py-12 px-4">
        {/* Header */}
        <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl shadow-lg border border-gray-700 mb-10 p-8 flex flex-col items-start">
          <h1 className="text-4xl font-extrabold text-pink-400 mb-2 tracking-tight">Google Trends Search Dashboard</h1>
          <p className="text-lg text-gray-300">Explore trending topics and search patterns using Google Trends API</p>
        </div>

        {/* Search Section */}
        <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl shadow-lg border border-gray-700 mb-10 p-6 flex flex-col gap-6">
          <div className="flex flex-col md:flex-row gap-4 items-center">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="e.g., coffee,tea,chocolate"
              className="flex-1 px-4 py-3 border border-gray-700 rounded-lg text-white bg-gray-950 focus:ring-2 focus:ring-pink-500"
            />
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="px-5 py-3 bg-gray-700 text-gray-200 rounded-lg hover:bg-gray-600 flex items-center gap-2 shadow"
            >
              <Settings size={20} />
              Settings
            </button>
            <button
              onClick={handleSearch}
              disabled={loading}
              className="px-7 py-3 bg-gradient-to-r from-pink-600 to-pink-500 text-white rounded-lg hover:from-pink-700 hover:to-pink-600 flex items-center gap-2 disabled:opacity-50 shadow"
            >
              {loading ? <Loader2 size={20} className="animate-spin" /> : <Search size={20} />}
              Search
            </button>
          </div>
          {/* Advanced Settings */}
          {showSettings && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-gray-800 rounded-lg border border-gray-700">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Data Type</label>
                <select
                  value={searchParams.data_type}
                  onChange={(e) => setSearchParams({ ...searchParams, data_type: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-700 rounded-lg focus:ring-2 focus:ring-pink-500 bg-gray-950 text-white"
                >
                  {dataTypes.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Time Range</label>
                <select
                  value={searchParams.date}
                  onChange={(e) => setSearchParams({ ...searchParams, date: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-700 rounded-lg focus:ring-2 focus:ring-pink-500 bg-gray-950 text-white"
                >
                  {dateOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Geographic Location</label>
                <select
                  value={searchParams.geo}
                  onChange={(e) => setSearchParams({ ...searchParams, geo: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-700 rounded-lg focus:ring-2 focus:ring-pink-500 bg-gray-950 text-white"
                >
                  {geoOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Language</label>
                <select
                  value={searchParams.hl}
                  onChange={(e) => setSearchParams({ ...searchParams, hl: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-700 rounded-lg focus:ring-2 focus:ring-pink-500 bg-gray-950 text-white"
                >
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                  <option value="de">German</option>
                  <option value="ja">Japanese</option>
                  <option value="hi">Hindi</option>
                </select>
              </div>
            </div>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-900/80 border border-red-700 text-red-200 px-4 py-3 rounded-xl mb-8 shadow">
            {error}
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl shadow-lg border border-gray-700 p-12 text-center">
            <Loader2 size={48} className="mx-auto animate-spin text-pink-400 mb-4" />
            <p className="text-gray-300">Fetching Google Trends data...</p>
          </div>
        )}

        {/* Results Section */}
        {data && !loading && (
          <div>
            {/* Search Metadata */}
            {data.search_metadata && (
              <div className="bg-white text-blue-900 rounded-lg shadow p-4 mb-6">
                <h3 className="text-lg font-semibold mb-2">Search Information</h3>
                <div className="grid grid-cols-1 font-bold md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-300">Status:</span> <span className="text-green-400">{data.search_metadata.status}</span>
                  </div>
                  <div>
                    <span className="font-medium text-orange-900">Query:</span> {data.search_parameters.q}
                  </div>
                  <div>
                    <span className="font-medium text-violet-800">Time taken:</span> {data.search_metadata.total_time_taken}s
                  </div>
                </div>
              </div>
            )}
            {/* Render Charts Based on Data Type */}
            {renderTimeSeriesChart()}
            {renderRegionBreakdown()}
            {renderInterestByRegion()}
            {renderRelatedTopics()}
            {renderRelatedQueries()}
          </div>
        )}

        {/* Instructions & Data Types */}
        {!data && !loading && (
          <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl shadow-lg border border-gray-700 p-8 mb-8">
            <h3 className="text-2xl font-semibold text-pink-400 mb-4">How to Use</h3>
            <ul className="space-y-2 text-gray-300">
              <li><span className="font-bold text-pink-400">1.</span> Enter your search terms separated by commas.</li>
              <li><span className="font-bold text-pink-400">2.</span> Configure settings for data type, time range, and location.</li>
              <li><span className="font-bold text-pink-400">3.</span> Analyze results with interactive charts.</li>
            </ul>
            <div className="mt-6 p-4 bg-pink-900/20 rounded-xl border border-pink-700">
              <h4 className="font-medium text-pink-400 mb-2">Data Types Available:</h4>
              <ul className="text-sm text-pink-300 space-y-1">
                <li><TrendingUp size={16} className="inline mr-1" /> <strong>Interest over time:</strong> Shows how search interest has changed over time</li>
                <li><Globe size={16} className="inline mr-1" /> <strong>Breakdown by region:</strong> Compares multiple queries across different regions</li>
                <li><Globe size={16} className="inline mr-1" /> <strong>Interest by region:</strong> Shows which regions have the most interest in a single query</li>
                <li><Calendar size={16} className="inline mr-1" /> <strong>Related topics:</strong> Displays topics related to your search query</li>
                <li><Search size={16} className="inline mr-1" /> <strong>Related queries:</strong> Shows queries related to your search term</li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default GoogleTrend;