import React, { useState } from 'react';

const supportTeamData = [
  {
    "id": "contact_1",
    "user_id": "user_1",
    "name": "<PERSON>",
    "profile_image": "https://images.unsplash.com/photo-*************-a303027c1d8b?w=500&h=500&fit=crop",
    "title": "Account Manager",
    "bio": "Handles your projects and business operations to ensure smooth workflow.",
    "support_areas": ["Account Management"],
    "timezone": "America/New_York",
    "availability": {
      "mon-fri": "09:00–17:00",
      "saturday": "Not available"
    },
    "chat_thread_id": "chat_101",
    "response_time_avg": "3h 30m",
    "last_activity": "2025-07-21T10:15:00Z"
  },
  {
    "id": "contact_2",
    "user_id": "user_2",
    "name": "<PERSON>",
    "profile_image": "https://images.unsplash.com/photo-*************-cad84cf45f1d?w=500&h=500&fit=crop",
    "title": "Technical Support",
    "bio": "Assists you with technical issues and helps with platform-related queries.",
    "support_areas": ["Technical Support"],
    "timezone": "America/Los_Angeles",
    "availability": {
      "mon-fri": "08:00–16:00",
      "saturday": "Not available"
    },
    "chat_thread_id": "chat_102",
    "response_time_avg": "2h 15m",
    "last_activity": "2025-07-21T14:20:00Z"
  },
  {
    "id": "contact_3",
    "user_id": "user_3",
    "name": "Emily Davis",
    "profile_image": "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=500&h=500&fit=crop",
    "title": "Content Strategy",
    "bio": "Guides your content strategy, planning, and execution.",
    "support_areas": ["Content Strategy"],
    "timezone": "Europe/London",
    "availability": {
      "mon-fri": "10:00–18:00",
      "saturday": "11:00–15:00"
    },
    "chat_thread_id": "chat_103",
    "response_time_avg": "5h 45m",
    "last_activity": "2025-07-20T18:05:00Z"
  },
  {
    "id": "contact_4",
    "user_id": "user_4",
    "name": "Daniel Smith",
    "profile_image": "https://images.unsplash.com/photo-1557862921-37829c790f19?w=500&h=500&fit=crop",
    "title": "Branding Specialist",
    "bio": "Provides support with building your brand and securing partnerships.",
    "support_areas": ["Branding"],
    "timezone": "Australia/Sydney",
    "availability": {
      "mon-fri": "09:00–17:00",
      "saturday": "Not available"
    },
    "chat_thread_id": "chat_104",
    "response_time_avg": "6h 5m",
    "last_activity": "2025-07-21T09:00:00Z"
  }
];

const SupportTeamMember = ({ member }) => {
  return (
    <div className="bg-gray-900 rounded-lg p-6 flex flex-col justify-between shadow-lg text-white">
      <div>
        <div className="flex items-center mb-4">
          <img src={member.profile_image} alt={member.name} className="w-16 h-16 rounded-full mr-4 object-cover" />
          <div>
            <h2 className="text-xl font-bold">{member.name}</h2>
            <p className="text-gray-400">{member.title}</p>
          </div>
        </div>
        <p className="text-gray-300 mb-6">{member.bio}</p>
      </div>
      <div className="flex justify-between items-center">
        <span className="text-xs font-semibold bg-gray-800 text-gray-300 px-3 p-2 rounded-full">{member.support_areas[0]}</span>
        <button onClick={() => window.location.href = `/model/supportsystem`} className="bg-gray-800 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition-all duration-300 ease-in-out hover:-translate-y-1">
          Message ➤
        </button>
      </div>
    </div>
  );
};

const Supportteam = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState('All Roles');

  const allRoles = ['All Roles', 'Account Management', 'Technical Support', 'Content Strategy', 'Branding'];

  const handleRoleSelect = (role) => {
    setSelectedRole(role);
    setIsOpen(false);
  };

  const filteredTeam = selectedRole === 'All Roles'
    ? supportTeamData
    : supportTeamData.filter(member => member.support_areas.includes(selectedRole));

  return (
    <div className="bg-black p-8 min-h-screen">
      <h1 className="text-4xl font-bold text-white mb-6">My Support Team</h1>
      <div className="relative inline-block text-left mb-8">
        <div>
          <button
            type="button"
            className="inline-flex justify-center w-full rounded-md border border-gray-700 shadow-sm px-4 py-2 bg-gray-900 text-sm font-medium text-white hover:bg-gray-800 focus:outline-none"
            id="options-menu"
            aria-haspopup="true"
            aria-expanded="true"
            onClick={() => setIsOpen(!isOpen)}
          >
            {selectedRole}
            <svg
              className="-mr-1 ml-2 h-5 w-5"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fillRule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>

        {isOpen && (
          <div className="origin-top-right absolute left-0 mt-2 w-56 rounded-md shadow-lg bg-gray-900 ring-1 ring-black ring-opacity-5">
            <div className="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
              {allRoles.map((role) => (
                <a
                  href="#"
                  key={role}
                  className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-800 hover:text-white"
                  role="menuitem"
                  onClick={() => handleRoleSelect(role)}
                >
                  {role}
                </a>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {filteredTeam.map(member => (
          <SupportTeamMember key={member.id} member={member} />
        ))}
      </div>
    </div>
  );
};

export default Supportteam;