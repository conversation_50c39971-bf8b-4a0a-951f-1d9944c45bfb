import { useEffect, useRef, useState } from "react";
import {
  Check,
  CheckCheck,
  Download,
  FileText,
  Music,
  Video,
  File,
  Plus,
  Play,
} from "lucide-react";
import { formateTime } from "../../utils/functions";

import { X, ChevronLeft, ChevronRight, Save } from "lucide-react";

const AttachmentRenderer = ({ attachments, isOwn }) => {
  const [galleryOpen, setGalleryOpen] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);

  const images = attachments.filter((att) => att.type === "image");
  const videos = attachments.filter((att) => att.type === "video");
  const otherFiles = attachments.filter(
    (att) => !["image", "video"].includes(att.type)
  );

  const mediaFiles = [...images, ...videos];

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!galleryOpen) return;

      if (e.key === "Escape") {
        setGalleryOpen(false);
      } else if (e.key === "ArrowLeft") {
        navigateGallery("prev");
      } else if (e.key === "ArrowRight") {
        navigateGallery("next");
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [galleryOpen]);

  // Handle touch gestures for mobile
  const minSwipeDistance = 50;

  const onTouchStart = (e) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe) {
      navigateGallery("next");
    } else if (isRightSwipe) {
      navigateGallery("prev");
    }
  };

  const getFileIcon = (mimeType) => {
    if (mimeType?.includes("pdf")) return <FileText className="w-8 h-8" />;
    if (mimeType?.includes("audio")) return <Music className="w-8 h-8" />;
    if (mimeType?.includes("video")) return <Video className="w-8 h-8" />;
    return <File className="w-8 h-8" />;
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return "";
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + " " + sizes[i];
  };

  const openGallery = (index) => {
    setCurrentIndex(index);
    setGalleryOpen(true);
  };

  const navigateGallery = (direction) => {
    if (direction === "next") {
      setCurrentIndex((prev) => (prev + 1) % mediaFiles.length);
    } else {
      setCurrentIndex(
        (prev) => (prev - 1 + mediaFiles.length) % mediaFiles.length
      );
    }
  };

  const downloadFile = (file) => {
    const url = file.localUrl || file.cloudinaryUrl;
    const link = document.createElement("a");
    link.href = url;
    link.download = file.name || "download";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const renderMediaGrid = () => {
    if (mediaFiles.length === 0) return null;

    if (mediaFiles.length === 1) {
      const media = mediaFiles[0];
      const url = media.localUrl || media.cloudinaryUrl;

      return (
        <div
          className="relative rounded-lg overflow-hidden max-w-xs max-h-80 cursor-pointer"
          onClick={() => openGallery(0)}
        >
          {media.type === "image" ? (
            <img
              src={url}
              alt={media.name || "Image"}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="video-div relative">
              <video
                src={url}
                className="w-full h-full object-cover"
                controls={false}
                onClick={(e) => {
                  e.stopPropagation();
                  openGallery(0);
                }}
              />
              <Play className="w-15 h-15 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"/>
            </div>
          )}
          {(media.status === "uploading" || media.status === "pending") && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
              <div className="bg-black bg-opacity-75 px-3 py-2 rounded-lg">
                <p className="text-white text-sm">
                  {media.status === "uploading"
                    ? `Uploading... ${media.progress || 0}%`
                    : "Uploading..."}
                </p>
              </div>
            </div>
          )}
        </div>
      );
    }

    if (mediaFiles.length === 2) {
      return (
        <div className="grid grid-cols-2 gap-1 rounded-lg overflow-hidden max-w-xs">
          {mediaFiles.slice(0, 2).map((media, index) => {
            const url = media.localUrl || media.cloudinaryUrl;
            return (
              <div
                key={index}
                className="relative aspect-square cursor-pointer"
                onClick={() => openGallery(index)}
              >
                {media.type === "image" ? (
                  <img
                    src={url}
                    alt={media.name || "Image"}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <video src={url} className="w-full h-full object-cover" />
                )}
                {(media.status === "uploading" ||
                  media.status === "pending") && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <div className="text-white text-xs">
                      {media.status === "uploading"
                        ? `${media.progress || 0}%`
                        : "..."}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      );
    }

    if (mediaFiles.length === 3) {
      return (
        <div className="grid grid-cols-2 gap-1 rounded-lg overflow-hidden max-w-xs">
          <div
            className="relative aspect-square cursor-pointer"
            onClick={() => openGallery(0)}
          >
            {mediaFiles[0].type === "image" ? (
              <img
                src={mediaFiles[0].localUrl || mediaFiles[0].cloudinaryUrl}
                alt={mediaFiles[0].name || "Image"}
                className="w-full h-full object-cover"
              />
            ) : (
              <video
                src={mediaFiles[0].localUrl || mediaFiles[0].cloudinaryUrl}
                className="w-full h-full object-cover"
              />
            )}
          </div>
          <div className="grid grid-rows-2 gap-1">
            {mediaFiles.slice(1, 3).map((media, index) => {
              const url = media.localUrl || media.cloudinaryUrl;
              return (
                <div
                  key={index}
                  className="relative aspect-square cursor-pointer"
                  onClick={() => openGallery(index + 1)}
                >
                  {media.type === "image" ? (
                    <img
                      src={url}
                      alt={media.name || "Image"}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <video src={url} className="w-full h-full object-cover" />
                  )}
                </div>
              );
            })}
          </div>
        </div>
      );
    }

    // 4 or more media files
    return (
      <div className="grid grid-cols-2 gap-1 rounded-lg overflow-hidden max-w-xs">
        {mediaFiles.slice(0, 3).map((media, index) => {
          const url = media.localUrl || media.cloudinaryUrl;
          return (
            <div
              key={index}
              className="relative aspect-square cursor-pointer"
              onClick={() => openGallery(index)}
            >
              {media.type === "image" ? (
                <img
                  src={url}
                  alt={media.name || "Image"}
                  className="w-full h-full object-cover"
                />
              ) : (
                <video src={url} className="w-full h-full object-cover" />
              )}
            </div>
          );
        })}
        <div
          className="relative aspect-square cursor-pointer"
          onClick={() => openGallery(3)}
        >
          {/* Show 4th image/video as background */}
          {mediaFiles[3].type === "image" ? (
            <img
              src={mediaFiles[3].localUrl || mediaFiles[3].cloudinaryUrl}
              alt={mediaFiles[3].name || "Image"}
              className="w-full h-full object-cover"
            />
          ) : (
            <video
              src={mediaFiles[3].localUrl || mediaFiles[3].cloudinaryUrl}
              className="w-full h-full object-cover"
            />
          )}
          {/* Overlay for additional files count */}
          {mediaFiles.length > 4 && (
            <div className="absolute inset-0 bg-black bg-opacity-70 flex items-center justify-center">
              <div className="text-center">
                <span className="text-white text-2xl font-bold">
                  +{mediaFiles.length - 4}
                </span>
                <div className="text-white text-sm font-medium mt-1">more</div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderOtherFiles = () => {
    if (otherFiles.length === 0) return null;

    return (
      <div className="space-y-2 mt-2">
        {otherFiles.map((file, index) => (
          <div
            key={index}
            className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${
              isOwn
                ? "bg-blue-600 bg-opacity-20 hover:bg-opacity-30"
                : "bg-gray-600 bg-opacity-20 hover:bg-opacity-30"
            }`}
          >
            <div
              className={`flex-shrink-0 ${
                isOwn ? "text-blue-200" : "text-gray-300"
              }`}
            >
              {getFileIcon(file.mimeType)}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">
                {file.name || "Unknown file"}
              </p>
              <p
                className={`text-xs ${
                  isOwn ? "text-blue-200" : "text-gray-400"
                }`}
              >
                {formatFileSize(file.size)} • {file.mimeType || "Unknown type"}
              </p>
              {(file.status === "uploading" || file.status === "pending") && (
                <div className="mt-1">
                  <div
                    className={`w-full bg-opacity-30 rounded-full h-1 ${
                      isOwn ? "bg-blue-300" : "bg-gray-300"
                    }`}
                  >
                    <div
                      className={`h-1 rounded-full transition-all duration-300 ${
                        isOwn ? "bg-blue-200" : "bg-gray-200"
                      }`}
                      style={{ width: `${file.progress || 0}%` }}
                    />
                  </div>
                  <p
                    className={`text-xs mt-1 ${
                      isOwn ? "text-blue-200" : "text-gray-400"
                    }`}
                  >
                    {file.status === "uploading"
                      ? `Uploading... ${file.progress || 0}%`
                      : "Processing..."}
                  </p>
                </div>
              )}
            </div>
            {file.status === "completed" && (
              <button
                onClick={() => downloadFile(file)}
                className={`p-2 rounded-full transition-colors ${
                  isOwn
                    ? "hover:bg-blue-500 hover:bg-opacity-30"
                    : "hover:bg-gray-500 hover:bg-opacity-30"
                }`}
              >
                <Download
                  className={`w-5 h-5 ${
                    isOwn ? "text-blue-200" : "text-gray-300"
                  }`}
                />
              </button>
            )}
          </div>
        ))}
      </div>
    );
  };

  const renderGallery = () => {
    if (!galleryOpen || mediaFiles.length === 0) return null;

    const currentMedia = mediaFiles[currentIndex];
    const url = currentMedia.localUrl || currentMedia.cloudinaryUrl;

    return (
      <div className="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center">
        {/* Close button */}
        <button
          onClick={() => setGalleryOpen(false)}
          className="absolute top-4 right-4 z-10 p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-colors"
        >
          <X className="w-6 h-6" />
        </button>

        {/* Save button */}
        <button
          onClick={() => downloadFile(currentMedia)}
          className="absolute top-4 right-16 z-10 p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-colors"
        >
          <Save className="w-6 h-6" />
        </button>

        {/* Navigation buttons */}
        {mediaFiles.length > 1 && (
          <>
            <button
              onClick={() => navigateGallery("prev")}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-3 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-colors"
            >
              <ChevronLeft className="w-8 h-8" />
            </button>
            <button
              onClick={() => navigateGallery("next")}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-3 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-colors"
            >
              <ChevronRight className="w-8 h-8" />
            </button>
          </>
        )}

        {/* Media content */}
        <div
          className="w-full h-full flex items-center justify-center p-8"
          onTouchStart={onTouchStart}
          onTouchMove={onTouchMove}
          onTouchEnd={onTouchEnd}
        >
          {currentMedia.type === "image" ? (
            <img
              src={url}
              alt={currentMedia.name || "Image"}
              className="max-w-[50vw] max-h-[80vh] w-auto h-auto object-contain"
            />
          ) : (
            <video
              src={url}
              className="max-w-[50vw] max-h-[80vh] w-auto h-auto object-contain"
              controls
              autoPlay
            />
          )}
        </div>

        {/* Counter */}
        {mediaFiles.length > 1 && (
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-4 py-2 rounded-full">
            {currentIndex + 1} of {mediaFiles.length}
          </div>
        )}

        {/* Media info */}
        <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white px-4 py-2 rounded-lg max-w-xs">
          <p className="text-sm font-medium truncate">
            {currentMedia.name || `${currentMedia.type} file`}
          </p>
          {currentMedia.size && (
            <p className="text-xs text-gray-300">
              {formatFileSize(currentMedia.size)}
            </p>
          )}
        </div>
      </div>
    );
  };

  return (
    <div>
      {renderMediaGrid()}
      {renderOtherFiles()}
      {renderGallery()}
    </div>
  );
};

const ChatMessage = ({
  msg,
  i,
  id,
  isOwn,
  convoType,
  msgType,
  position,
  expandedMessages = {},
  toggleReadMore,
  handleMessageSeen,
  handleRightClick,
}) => {
  const messageRef = useRef(null);
  const OwnerSingleMsgStyles = "";
  const OwnerTopMsgStyles = "rounded-br-sm";
  const OwnerCenterMsgStyles = "rounded-tr-md rounded-br-md";
  const OwnerBottomMsgStyles = "rounded-tr-sm";

  const NotOwnerSingleMsgStyles = "";
  const NotOwnerTopMsgStyles = "rounded-bl-sm";
  const NotOwnerCenterMsgStyles = "rounded-tl-md rounded-bl-md";
  const NotOwnerBottomMsgStyles = "rounded-tl-sm";

  useEffect(() => {
    if (isOwn || msg.status === "seen") return;
    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        if (entry.isIntersecting) {
          handleMessageSeen?.(msg);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
      }
    );

    if (messageRef.current) {
      observer.observe(messageRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  const hasText = msg.text && msg.text.trim().length > 0;
  const hasAttachments = msg.attachments && msg.attachments.length > 0;
  const otherFiles =
    msg.attachments.filter((att) => !["image", "video"].includes(att.type))
      .length > 0;

  return (
    <div
      key={i}
      ref={messageRef}
      className={`flex w-full my-1 ${isOwn ? "justify-end" : "justify-start"}`}
    >
      <div
        onContextMenu={(e) => handleRightClick?.(e, msg, isOwn)}
        className={`flex flex-col min-w-[10%] w-fit max-w-[70%] rounded-2xl relative shadow-lg overflow-clip ${
          isOwn
            ? `bg-gradient-to-br from-blue-700 to-blue-500 text-white ml-8 ${
                position === "top" ? OwnerTopMsgStyles : ""
              } ${position === "center" ? OwnerCenterMsgStyles : ""} ${
                position === "bottom" ? OwnerBottomMsgStyles : ""
              }`
            : `bg-gradient-to-br from-gray-700 to-gray-700 text-gray-100 mr-2 ${
                position === "top" ? NotOwnerTopMsgStyles : ""
              } ${position === "center" ? NotOwnerCenterMsgStyles : ""} ${
                position === "bottom" ? NotOwnerBottomMsgStyles : ""
              }`
        }`}
        style={{
          boxShadow: isOwn
            ? "0 4px 16px 0 rgba(0, 60, 255, 0.12)"
            : "0 4px 16px 0 rgba(0,0,0,0.12)",
        }}
      >
        {/* Render attachments */}
        {hasAttachments && (
          <div className={`${hasText ? "mb-2" : "mb-0"} p-1`}>
            <AttachmentRenderer attachments={msg.attachments} isOwn={isOwn} />
          </div>
        )}

        {/* Message Text */}
        {hasText && (
          <div className="px-3 py-2">
            <div className="flex max-w-full flex-wrap items-end">
              <p className="leading-relaxed text-[1rem] font-medium break-words whitespace-pre-line">
                {msg.text.length > 500 && !expandedMessages[i] ? (
                  <>
                    {msg.text.slice(0, 500)}...
                    <button
                      onClick={() => toggleReadMore?.(i)}
                      className="text-sm font-medium text-green-300 underline ml-1"
                    >
                      Read more
                    </button>
                  </>
                ) : (
                  <>
                    {msg.text}
                    {msg.text.length > 500 && (
                      <button
                        onClick={() => toggleReadMore?.(i)}
                        className="text-sm font-medium text-green-300 underline ml-1"
                      >
                        Read less
                      </button>
                    )}
                  </>
                )}
              </p>

              <span
                className={`text-xs ml-auto pl-2 pt-[2px] flex flex-nowrap items-end gap-1 font-medium ${
                  isOwn ? "text-blue-200" : "text-gray-400"
                }`}
              >
                {formateTime(msg.createdAt, "msg")}{" "}
                {isOwn && msg.status === "sent" ? (
                  <Check className="" size={20} />
                ) : isOwn && msg.status === "seen" ? (
                  <CheckCheck className="text-white" size={20} />
                ) : null}
              </span>
            </div>
          </div>
        )}

        {/* If only image video attachments, show timestamp at bottom */}
        {!hasText && hasAttachments && !otherFiles && (
          <div className=" absolute bottom-0 right-0 px-3 py-2 pt-0">
            <div className="flex justify-end">
              <span
                className={`text-xs flex flex-nowrap items-end gap-1 font-medium ${
                  isOwn ? "text-blue-200" : "text-gray-400"
                }`}
              >
                {formateTime(msg.createdAt, "msg")}{" "}
                {isOwn && msg.status === "sent" ? (
                  <Check className="" size={20} />
                ) : isOwn && msg.status === "seen" ? (
                  <CheckCheck className="text-white" size={20} />
                ) : null}
              </span>
            </div>
          </div>
        )}

        {/* If only document attachments, or other files show timestamp below files */}
        {!hasText && hasAttachments && otherFiles && (
          <div className="  px-3 py-2 pt-0">
            <div className="flex justify-end">
              <span
                className={`text-xs flex flex-nowrap items-end gap-1 font-medium ${
                  isOwn ? "text-blue-200" : "text-gray-400"
                }`}
              >
                {formateTime(msg.createdAt, "msg")}{" "}
                {isOwn && msg.status === "sent" ? (
                  <Check className="" size={20} />
                ) : isOwn && msg.status === "seen" ? (
                  <CheckCheck className="text-white" size={20} />
                ) : null}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatMessage;
