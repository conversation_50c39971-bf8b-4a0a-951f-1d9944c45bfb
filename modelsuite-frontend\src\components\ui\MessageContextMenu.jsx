import { Reply, Edit3, <PERSON><PERSON>, <PERSON><PERSON>, Trash2, Plus } from "lucide-react";
import Picker from "emoji-picker-react";
import { useEffect, useRef, useState } from "react";

export const MessageContextMenu = ({
  
  isOpen,
  onClose,
  message,
  isOwn,
  onReact,
  onReply,
  onEdit,
  onPin,
  onCopyText,
  onDelete,
}) => {
  const menuRef = useRef(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);

  // Common reaction emojis
  const reactions = ["😂","👍", "❤️", "🔥", "😢", "👏"];

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        onClose();
        setShowEmojiPicker(false);
      }
    };

    const handleEscape = (event) => {
      if (event.key === "Escape") {
        if (showEmojiPicker) {
          setShowEmojiPicker(false);
        } else {
          onClose();
        }
      }
    };

    if (isOpen || showEmojiPicker) {
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("keydown", handleEscape);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEscape);
    };
  }, [isOpen, onClose, showEmojiPicker]);

  if (!isOpen && !showEmojiPicker) return null;

  const handleReaction = (emoji) => {
    onReact(message, emoji);
    onClose();
  };

  const handleEmojiSelect = (emojiObject) => {
    onReact(message, emojiObject.emoji);
    setShowEmojiPicker(false);
    onClose();
  };

  const handleEmojiPickerOpen = () => {
    setShowEmojiPicker(true);
    // Close the menu when opening emoji picker
    onClose();
  };

  return (
    <>
      {/* Main Context Menu */}
     
        <div className="flex justify-center items-center w-full h-full absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50 bg-[#00000057]">
          {isOpen && !showEmojiPicker && (
          <div
            ref={menuRef}
            className=" bg-white dark:bg-[#2b2d31] border border-gray-200 dark:border-[#3f4248] rounded-lg  min-w-[180px] overflow-hidden animate-in fade-in-0 zoom-in-95 duration-150"
          >
            {/* Reactions Row - Telegram Style */}
            <div className="flex items-center gap-1 px-2 py-2 bg-gray-50 dark:bg-[#36393f] border-b border-gray-200 dark:border-[#3f4248]">
              {reactions.map((emoji) => (
                <button
                  key={emoji}
                  onClick={() => handleReaction(emoji)}
                  className="text-lg hover:scale-110 transition-transform duration-150 p-1.5 hover:bg-gray-200 dark:hover:bg-[#40444b] rounded-md"
                >
                  {emoji}
                </button>
              ))}
              <div className="w-px h-5 bg-gray-300 dark:bg-[#4f545c] mx-1" />
              <button
                onClick={handleEmojiPickerOpen}
                className="p-1.5 hover:bg-gray-200 dark:hover:bg-[#40444b] rounded-md transition-colors duration-150"
              >
                <Plus size={16} className="text-gray-500 dark:text-gray-400" />
              </button>
            </div>

            {/* Menu Items */}
            <div className="py-1">
              <MenuItem
                icon={<Reply size={16} />}
                text="Reply"
                onClick={() => {
                  onReply(message);
                  onClose();
                }}
              />

              {isOwn && (
                <MenuItem
                  icon={<Edit3 size={16} />}
                  text="Edit"
                  onClick={() => {
                    onEdit(message._id);
                    onClose();
                  }}
                />
              )}

              <MenuItem
                icon={<Pin size={16} />}
                text="Pin"
                onClick={() => {
                  onPin(message._id);
                  onClose();
                }}
              />

              <MenuItem
                icon={<Copy size={16} />}
                text="Copy Text"
                onClick={() => {
                  onCopyText(message._id);
                  onClose();
                }}
              />

              {isOwn && (
                <>
                  <div className="h-px bg-gray-200 dark:bg-[#3f4248] my-1" />
                  <MenuItem
                    icon={<Trash2 size={16} />}
                    text="Delete"
                    onClick={() => {
                      onDelete(message._id);
                      onClose();
                    }}
                    danger
                  />
                </>
              )}
            </div>
          </div>
          )}
          {/* Emoji Picker */}
          {showEmojiPicker && (
            <div className="z-60 animate-in fade-in-0 zoom-in-95 duration-150">
              <div ref={menuRef}>
                <Picker
                  onEmojiClick={handleEmojiSelect}
                  theme="auto"
                  previewConfig={{
                    showPreview: false,
                  }}
                  skinTonesDisabled
                  searchDisabled
                  height={350}
                  width={300}
                />
              </div>
            </div>
          )}
          
        </div>
      
    </>
  );
};

// Clean Menu Item Component
const MenuItem = ({ icon, text, onClick, danger = false }) => (
  <button
    onClick={onClick}
    className={`w-full flex items-center gap-3 px-3 py-2.5 transition-colors duration-150 text-left text-sm ${
      danger
        ? "text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20"
        : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-[#40444b]"
    }`}
  >
    <span
      className={`${
        danger
          ? "text-red-600 dark:text-red-400"
          : "text-gray-500 dark:text-gray-400"
      }`}
    >
      {icon}
    </span>
    <span className="font-medium">{text}</span>
  </button>
);
