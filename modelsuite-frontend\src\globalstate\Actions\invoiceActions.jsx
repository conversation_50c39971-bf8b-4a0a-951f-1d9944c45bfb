import {
  getDataAP<PERSON>,
  postDataAP<PERSON>,
  putDataAPI,
  deleteDataAP<PERSON>,
} from "../../utils/fetchData.jsx"; 
import { toast } from "react-hot-toast";

import {
  setLoading,
  setError,
  setInvoices,
  addInvoice,
  setCurrentInvoice,
  updateInvoiceInState,
  deleteInvoiceFromState,
} from "../invoiceSlice";

// ✅ Upload Invoice
export const uploadInvoiceAction = (formData) => async (dispatch) => {
  try {
    dispatch(setLoading(true));
    const res = await postDataAPI("billing/invoice", formData);
    dispatch(addInvoice(res.data.invoice));
    toast.success("Invoice uploaded successfully!");
  } catch (err) {
    dispatch(setError(err.response?.data?.message || "Upload failed"));
    toast.error(err.response?.data?.message || "Upload failed");
  } finally {
    dispatch(setLoading(false));
  }
};


// ✅ Get All Invoices
// Modify your getInvoicesAction to accept page & limit
export const getInvoicesAction = (page = 1, limit = 5) => async (dispatch) => {
  try {
    dispatch(setLoading(true));
    const res = await getDataAPI(`billing/invoices?page=${page}&limit=${limit}`);
    dispatch(setInvoices(res.data));  // data should contain invoices, totalPages, totalInvoices
  } catch (err) {
    dispatch(setError(err.response?.data?.message || "Fetching failed"));
    toast.error(err.response?.data?.message || "Fetching failed");
  } finally {
    dispatch(setLoading(false));
  }
};



// ✅ Get Single Invoice by ID
export const getInvoiceByIdAction = (id) => async (dispatch) => {
  try {
    const res = await getDataAPI(`billing/invoices/${id}`);
    dispatch(setCurrentInvoice(res.data.data));
  } catch (err) {
    dispatch(setError(err.response?.data?.message || "Invoice not found"));
  }
};

// ✅ Update Invoice
export const updateInvoiceAction = (id, data) => async (dispatch) => {
  try {
    await putDataAPI(`billing/invoices/${id}`, data);
    dispatch(updateInvoiceInState({ id, data }));
    toast.success("Invoice updated!");
  } catch (err) {
    dispatch(setError(err.response?.data?.message || "Update failed"));
    toast.error(err.response?.data?.message || "Update failed");
  }
};

// ✅ Delete Invoice
export const deleteInvoiceAction = (id) => async (dispatch) => {
  try {
    await deleteDataAPI(`billing/invoices/${id}`);
    dispatch(deleteInvoiceFromState(id));
    toast.success("Invoice deleted");
  } catch (err) {
    dispatch(setError(err.response?.data?.message || "Delete failed"));
    toast.error(err.response?.data?.message || "Delete failed");
  }
};
// ✅ Export CSV
export const exportInvoicesAction = () => async () => {
  try {
    const res = await getDataAPI("invoices/export");
    console.log(res.data);
    if (res.data?.downloadUrl) {
      // Trigger browser download
      const link = document.createElement("a");
      link.href = res.data.downloadUrl;
      link.setAttribute("download", "invoices.csv");
      document.body.appendChild(link);
      link.click();
      link.remove();
    } else {
      toast.error("Download URL not available");
    }
  } catch (err) {
    console.error("CSV Export Error:", err);
    toast.error(err.response?.data?.message || "CSV export failed");
  }
};

export const searchModelsAction = (query) => async (dispatch) => {
  try {
    dispatch(setLoading(true));

    const token = JSON.parse(localStorage.getItem("auth"))?.token;
    if (!token) throw new Error("Token not found");

    const res = await getDataAPI(`/agency/models?search=${query}`, token);
    return res.data;
  } catch (err) {
    dispatch(setError(err.response?.data?.message || "Model search failed"));
    toast.error(err.response?.data?.message || "Model search failed");
    return [];
  } finally {
    dispatch(setLoading(false));
  }
};

