import { configureStore } from "@reduxjs/toolkit";
import invoiceReducer from "./invoiceSlice.jsx";
import alertReducer from "./alertSlice.jsx";
import dmReducer from "./dmSlice";
import groupReducer from "./groupSlice";
import channelReducer from "./channelSlice";
import modelsReducer from './modelsSlice';

const store = configureStore({
  reducer: {
    invoice: invoiceReducer,
    alert: alertReducer,
    dm: dmReducer,
    group: groupReducer,
    channel: channelReducer,
    models: modelsReducer,
  },
});

export default store;
