import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  uploadInvoiceAction,
  getInvoicesAction,
  exportInvoicesAction,
  updateInvoiceAction,
} from "../../globalstate/Actions/invoiceActions.jsx";

import Header from "../../components/Billing/Header.jsx";
import SearchBar from "../../components/Billing/SearchBar.jsx";
import InvoiceTable from "../../components/Billing/InvoiceTable.jsx";
import InvoiceModal from "../../components/Billing/InvoiceModal.jsx";
import Pagination from "../../components/Billing/Pagination.jsx";

const BillingUIDemo = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [formData, setFormData] = useState({
    client: "",
    campaign: "",
    amount: "",
    currency: "INR",
    dueDate: "",
    status: "Unpaid",
    note: "",
    file: null,
  });
  const [page, setPage] = useState(1);
  const limit = 5;
  const {
    invoices,
    loading,
    currentInvoice,
    error,
    totalPages,
    totalInvoices,
  } = useSelector((state) => state.invoice);
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(getInvoicesAction(page, limit));
  }, [dispatch, page]);
  const handlePrev = () => {
    if (page > 1) setPage((prev) => prev - 1);
  };

  const handleNext = () => {
    if (page < totalPages) setPage((prev) => prev + 1);
  };

  // useCallback to prevent unnecessary re-creation
  const handleChange = useCallback((e) => {
    const { name, value, files } = e.target;
    setFormData((prev) => ({ ...prev, [name]: files ? files[0] : value }));
  }, []);

  const handleSubmit = useCallback(
    async (e) => {
      e.preventDefault();
      const form = new FormData();
      for (let key in formData) {
        form.append(key, formData[key]);
      }
      dispatch(uploadInvoiceAction(form));
      setShowModal(false);
    },
    [dispatch, formData]
  );

  const handleModalOpen = useCallback(() => setShowModal(true), []);
  const handleModalClose = useCallback(() => setShowModal(false), []);

  // Memoize filtered results
  const filtered = useMemo(() => {
    const query = searchQuery.toLowerCase();
    return (
      invoices?.filter((inv) => {
        const clientName = inv.client?.fullName?.toLowerCase() || "";
        const username = inv.client?.username?.toLowerCase() || "";
        const status = inv.status?.toLowerCase() || "";

        return (
          clientName.includes(query) ||
          username.includes(query) ||
          status.includes(query)
        );
      }) || []
    );
  }, [searchQuery, invoices]);
  const handleEditInvoice = useCallback((id, updatedData) => {
  dispatch(updateInvoiceAction(id, updatedData));
}, [dispatch]);


  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="max-w-[1280px] mx-auto px-6 py-8">
        <Header title="Billing" onClick={handleModalOpen} />
        <SearchBar searchQuery={searchQuery} setSearchQuery={setSearchQuery} />

       <InvoiceTable invoices={filtered} loading={loading} onEdit={handleEditInvoice} />
        {/* Pagination */}
        <Pagination
          page={page}
          totalPages={totalPages}
          onPrev={handlePrev}
          onNext={handleNext}
        />

        {showModal && (
          <InvoiceModal
            onClose={handleModalClose}
            onSubmit={handleSubmit}
            formData={formData}
            setFormData={setFormData}
            handleChange={handleChange}
          />
        )}
      </div>
    </div>
  );
};

export default BillingUIDemo;
