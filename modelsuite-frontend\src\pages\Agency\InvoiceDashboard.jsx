import React from "react";

const Dashboard = () => {
  return (
    <div className="p-6 bg-gradient-to-tr from-slate-200 to-slate-300min-h-screen">
      <h1 className="text-3xl font-bold mb-6 text-gray-800">Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <div className="bg-white p-4 rounded-xl shadow">
          <h2 className="text-gray-500 text-sm font-medium">Outstanding Invoices</h2>
          <p className="text-2xl font-bold text-gray-800">₹10,000</p>
        </div>
        <div className="bg-white p-4 rounded-xl shadow">
          <h2 className="text-gray-500 text-sm font-medium">Total Earnings</h2>
          <p className="text-2xl font-bold text-green-600">₹30,000</p>
        </div>
        <div className="bg-white p-4 rounded-xl shadow">
          <h2 className="text-gray-500 text-sm font-medium">Paid Invoices</h2>
          <p className="text-2xl font-bold text-blue-600">₹20,000</p>
        </div>
        <div className="bg-white p-4 rounded-xl shadow">
          <h2 className="text-gray-500 text-sm font-medium">Pending Invoices</h2>
          <p className="text-2xl font-bold text-yellow-500">₹5,000</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="bg-white p-4 rounded-xl shadow col-span-2">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">Activity Feed</h3>
          <ul className="space-y-2">
            <li className="text-sm text-gray-600">Invoice #123 marked as paid</li>
            <li className="text-sm text-gray-600">Invoice #124 sent to client</li>
            <li className="text-sm text-gray-600">Invoice #125 overdue</li>
          </ul>
        </div>

        <div className="bg-white p-4 rounded-xl shadow">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">Tax Summary</h3>
          <p className="text-sm text-gray-600">GST Collected: ₹4,500</p>
          <p className="text-sm text-gray-600">GST Paid: ₹2,000</p>
        </div>
      </div>

      <div className="bg-white mt-8 p-4 rounded-xl shadow">
        <h3 className="text-lg font-semibold text-gray-700 mb-4">Recent Transactions</h3>
        <table className="w-full table-auto text-left">
          <thead>
            <tr>
              <th className="text-sm text-gray-500 font-medium pb-2">Invoice ID</th>
              <th className="text-sm text-gray-500 font-medium pb-2">Amount</th>
              <th className="text-sm text-gray-500 font-medium pb-2">Status</th>
              <th className="text-sm text-gray-500 font-medium pb-2">Date</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            <tr>
              <td className="py-2 text-sm text-gray-800">INV-123</td>
              <td className="py-2 text-sm text-gray-800">₹4,500</td>
              <td className="py-2 text-sm text-green-600">Paid</td>
              <td className="py-2 text-sm text-gray-600">20 July 2025</td>
            </tr>
            <tr>
              <td className="py-2 text-sm text-gray-800">INV-124</td>
              <td className="py-2 text-sm text-gray-800">₹3,000</td>
              <td className="py-2 text-sm text-yellow-600">Pending</td>
              <td className="py-2 text-sm text-gray-600">21 July 2025</td>
            </tr>
            <tr>
              <td className="py-2 text-sm text-gray-800">INV-125</td>
              <td className="py-2 text-sm text-gray-800">₹2,500</td>
              <td className="py-2 text-sm text-red-600">Overdue</td>
              <td className="py-2 text-sm text-gray-600">22 July 2025</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Dashboard;
