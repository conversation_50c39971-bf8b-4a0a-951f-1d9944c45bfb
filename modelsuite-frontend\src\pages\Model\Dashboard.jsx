/* eslint-disable no-unused-vars */
import React, { useEffect, useState, useCallback } from "react";
import { useNavigate, Link, useLocation } from "react-router-dom";
import axios from "axios";
import ChatWindow from "../../components/ChatWindow";
import ModelTaskList from "../../components/task/TaskList";

import {
  LogOut,
  MessageSquare,
  Users,
  Calendar,
  Settings,
  Plus,
  TrendingUp,
  CalendarCheck,
  CircleUser,
  Search,
  HelpCircle,
  X,
  Upload,
  FileText,
  ClipboardList,
} from "lucide-react";
import BoardsView from "../../components/task/board/BoardsView";
import InstagramDashboard from "../../components/socialMedia/InstagramInsights";
import ModelCalender from "../../components/Calender/ModelCalendar2";
import MyProfile from "../../components/MyProfile/components";
import { formateTime, useTypingIndicator } from "../../utils/functions";
import socket from "../../utils/socket";
import ContentUpload from "../../components/contentUpload/main";
import TypingIndicator from "../../components/ui/TypingIndicator";
import {
  Button,
  Avatar,
  Badge,
  Modal,
  SearchInput,
  Tooltip,
} from "../../components/ui";

import ModelMessanger from "../../components/modelMessanger";

import AssignedQuestionnaires from "../../components/questionnaire/model/AssignedQuestionnaires";
import Supportteam from "../../components/supportteam/Supportteam";

export default function ModelDashboard() {
  const user = JSON.parse(localStorage.getItem("auth"))?.user;
  const token = JSON.parse(localStorage.getItem("auth"))?.token;
  const baseURL = import.meta.env.VITE_API_BASE_URL;

  const [activeTab, setActiveTab] = useState("Messenger");
  const [groupList, setGroupList] = useState([]);
  const [topicsMap, setTopicsMap] = useState({});
  const [agencyInfo, setAgencyInfo] = useState("");
  const [agencyStatus, setAgencyStatus] = useState({
    isOnline: null,
    isTyping: null,
    lastOnline: null,
  });
  const [selectedChat, setSelectedChat] = useState(null);

  // Additional state from second file
  const [models, setModels] = useState([]);
  const [isAddModalOpen, setIsAddModelOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [showMenu, setShowMenu] = useState(false);

  const navigate = useNavigate();
  const location = useLocation();
  const handleTyping = useTypingIndicator(socket, user._id);

  // Check if we're on the questionnaires route
  const isQuestionnairesRoute = location.pathname === "/model/questionnaires";
  const logout = () => {
    localStorage.removeItem("auth");
    localStorage.removeItem("token");
    localStorage.removeItem("tokenExpiry");
    navigate("/");
  };

  const handleLogout = () => {
    localStorage.removeItem("auth");
    localStorage.removeItem("token");
    localStorage.removeItem("tokenExpiry");
    navigate("/");
  };

  const fetchGroupsAndTopics = useCallback(async () => {
    try {
      const res = await axios.get(
        `${baseURL}/messages/group?modelId=${user._id}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      setGroupList(res.data);

      const topicMap = {};
      for (const group of res.data) {
        const topicRes = await axios.get(
          `${baseURL}/topic/group/${group._id}`,
          {
            headers: { Authorization: `Bearer ${token}` },
          }
        );
        topicMap[group._id] = topicRes.data;
      }
      setTopicsMap(topicMap);
    } catch (err) {
      console.error("❌ Failed to fetch groups/topics:", err);
    }
  }, [baseURL, user._id, token]);

  const fetchAgencyStatus = useCallback(async () => {
    socket.emit("fetch_isUserOnline", { userId: user.agencyId });
  }, [user.agencyId]);

  const fetchAgencyAvatar = useCallback(async () => {
    try {
      const res = await axios.get(
        `${baseURL}/messages/agency-to-model/${user.agencyId}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      setAgencyInfo(res.data);
    } catch (err) {
      console.error("❌ Failed to fetch agency avatar:", err);
      return null;
    }
  }, [baseURL, user.agencyId, token]);

  const fetchAgencyModels = useCallback(async () => {
    try {
      const res = await axios.get(`${baseURL}/agency/agency-models`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return res.data;
    } catch (err) {
      console.error("Failed to fetch agency models:", err);
      return [];
    }
  }, [baseURL, token]);

  const handleSearchAgencyModels = useCallback(
    (query) => {
      setSearchQuery(query);

      if (!query.trim()) {
        setSearchResults([]);
        return;
      }

      const filtered = models.filter((model) => {
        const fullNameMatch = model.fullName
          .toLowerCase()
          .includes(query.toLowerCase());
        const usernameMatch = model.username
          .toLowerCase()
          .includes(query.toLowerCase());
        return fullNameMatch || usernameMatch;
      });

      setSearchResults(filtered);
    },
    [models]
  );

  const handleModelClick = (id) => {
    navigate(`/agency/model-view/${id}`);
    setIsSearchOpen(false);
  };

  const handleGlobalModelSearch = async (query) => {
    setSearchQuery(query);

    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      const res = await axios.get(`${baseURL}/agency/models?search=${query}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      setSearchResults(res.data);
    } catch (err) {
      console.error("Global model search failed:", err);
      setSearchResults([]);
    }
  };

  const handleAddModelToAgency = async () => {
    if (!selectedUser) return;

    try {
      // eslint-disable-next-line no-unused-vars
      const res = await axios.post(
        `${baseURL}/agency/add-model`,
        {
          modelId: selectedUser._id,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      alert(`✅ ${selectedUser.fullName} has been added to your agency`);
      setSelectedUser(null);
      setSearchQuery("");
      setSearchResults([]);
      setIsAddModelOpen(false);

      const updatedModels = await fetchAgencyModels();
      setModels(updatedModels);
    } catch (err) {
      console.error("Add to agency failed:", err);
      alert("❌ Failed to add model to agency");
    }
  };

  const openModal = () => {
    setIsAddModelOpen(true);
    setSearchQuery("");
    setSelectedUser(null);
  };

  const closeModal = () => {
    setSelectedUser(null);
    setSearchQuery("");
    setSearchResults([]);
    setIsAddModelOpen(false);
  };

  const closeSearch = () => {
    setIsSearchOpen(false);
    setSearchQuery("");
    setSearchResults([]);
  };

  useEffect(() => {
    socket.on("update_isUserOnline", (data) => {
      console.log("got agency astatus", data);
      if (data.userId != user.agencyId) return;

      const formattedLastOnline = formateTime(data.lastOnline);

      setAgencyStatus((prev) => ({
        ...prev,
        isOnline: data.status,
        lastOnline: formattedLastOnline,
      }));
    });

    socket.on("update_typing_status", (data) => {
      console.log("typing reached frontend");
      if (data.userId != user.agencyId) return;
      setAgencyStatus((prev) => ({ ...prev, isTyping: data.isTyping }));
    });

    fetchGroupsAndTopics();
    fetchAgencyStatus();
    fetchAgencyAvatar();

    const getModels = async () => {
      const data = await fetchAgencyModels();
      setModels(data);
    };
    getModels();

    return () => {
      socket.off("update_isUserOnline");
      socket.off("update_typing_status");
    };
  }, [
    fetchAgencyAvatar,
    fetchAgencyModels,
    fetchAgencyStatus,
    fetchGroupsAndTopics,
    user.agencyId,
  ]);

  useEffect(() => {
    handleSearchAgencyModels(searchQuery);
  }, [handleSearchAgencyModels, searchQuery]);

  // Reset activeTab when on questionnaires route
  useEffect(() => {
    if (isQuestionnairesRoute) {
      setActiveTab("");
    }
  }, [isQuestionnairesRoute]);

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    setSelectedChat(null);

    // If we're on questionnaires route, navigate back to dashboard
    if (isQuestionnairesRoute) {
      navigate("/model/dashboard");
    }
  };

  return (
    <>
      {/* Unified Sidebar from second file */}
      <div className="fixed left-0 top-0 h-full w-20 bg-gradient-to-br from-gray-950 via-gray-900 to-gray-950 flex flex-col items-center py-6 space-y-4 shadow-xl border-r border-gray-800 z-30">
        <Tooltip text="Search">
          <Button
            variant="ghost"
            size="icon"
            className="mb-2 hover:bg-blue-600/20"
            onClick={() => setIsSearchOpen(true)}
          >
            <Search className="h-5 w-5 text-blue-400" />
          </Button>
        </Tooltip>
        <Tooltip text="Add Model">
          <Button
            variant="ghost"
            size="icon"
            className="hover:bg-green-600/20"
            onClick={openModal}
          >
            <Plus className="h-5 w-5 text-green-400" />
          </Button>
        </Tooltip>
        <div className="flex-1 space-y-4 mt-10">
          {models.map((model, index) => (
            <Tooltip key={model._id} text={model.fullName}>
              <div className="relative flex flex-col items-center">
                <Link to={`/agency/model-view/${model._id}`}>
                  <Avatar
                    src={model.profilePhoto}
                    alt={model.fullName}
                    fallback={`U${index + 1}`}
                    className="h-12 w-12 border-2 border-blue-600 shadow"
                  />
                </Link>
                {index === 3 && (
                  <Badge
                    className="absolute -top-1 -right-1 h-5 w-5 rounded-full"
                    color="red"
                  >
                    4
                  </Badge>
                )}
              </div>
            </Tooltip>
          ))}
        </div>
        <div className="space-y-4 mb-2">
          <Tooltip text="Help">
            <Link to="/support/faqs">
              <Button
                variant="ghost"
                size="icon"
                className="hover:bg-yellow-600/20"
              >
                <HelpCircle className="h-5 w-5 text-yellow-400" />
              </Button>
            </Link>
          </Tooltip>
          <Tooltip text="Settings">
            <Button
              variant="ghost"
              size="icon"
              className="hover:bg-gray-600/20"
              onClick={() => setShowMenu(!showMenu)}
            >
              <Settings className="h-5 w-5 text-gray-400" />
            </Button>
          </Tooltip>
        </div>
        {showMenu && (
          <div className="absolute bottom-20 left-20 w-36 bg-white text-gray-900 rounded-xl shadow-lg z-50 border border-gray-200 animate-fade-in overflow-hidden">
            <button
              onClick={handleLogout}
              className="block w-full text-left px-4 py-3 hover:bg-gray-100 font-medium border-b border-gray-200"
            >
              Logout
            </button>
            <button
              onClick={() => setShowMenu(false)}
              className="block w-full text-left px-4 py-3 hover:bg-gray-100"
            >
              Close
            </button>
          </div>
        )}
      </div>

      {/* Main Dashboard Content */}
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 pt-16 text-white" style={{ paddingLeft: '4%' }}>
        <div className="flex w-full">
          {/* Unified Sidebar for Model Dashboard */}
          <aside className="w-64 min-h-[calc(100vh-4rem)] bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border-r border-gray-800 rounded-r-3xl shadow-lg p-6 flex flex-col justify-between mt-4">
            <div>
              <h1 className="text-2xl font-bold mb-8 tracking-tight">
                Model Dashboard
              </h1>
              <nav className="space-y-2">
                <button
                  onClick={() => handleTabChange("Messenger")}
                  className={`w-full flex items-center px-4 py-3 rounded-xl font-medium transition-colors ${activeTab === "Messenger"
                    ? "bg-blue-600 text-white shadow"
                    : "hover:bg-gray-800 text-gray-300"
                    }`}
                >
                  <MessageSquare className="w-5 h-5 mr-3" /> Messenger
                </button>
                <button
                  onClick={() => handleTabChange("Billing & Finance")}
                  className={`w-full flex items-center px-4 py-3 rounded-xl font-medium transition-colors ${activeTab === "Billing & Finance"
                    ? "bg-blue-600 text-white shadow"
                    : "hover:bg-gray-800 text-gray-300"
                    }`}
                >
                  <Calendar className="w-5 h-5 mr-3" /> Billing & Finance
                </button>
                <button
                  onClick={() => handleTabChange("Tasks")}
                  className={`w-full flex items-center px-4 py-3 rounded-xl font-medium transition-colors ${activeTab === "Tasks"
                    ? "bg-blue-600 text-white shadow"
                    : "hover:bg-gray-800 text-gray-300"
                    }`}
                >
                  <CalendarCheck className="w-5 h-5 mr-3" /> Tasks
                </button>
                <button
                  onClick={() => handleTabChange("Traffic & Analytics")}
                  className={`w-full flex items-center px-4 py-3 rounded-xl font-medium transition-colors ${activeTab === "Traffic & Analytics"
                    ? "bg-blue-600 text-white shadow"
                    : "hover:bg-gray-800 text-gray-300"
                    }`}
                >
                  <TrendingUp className="w-5 h-5 mr-3" /> Traffic & Analytics
                </button>
                <button
                  onClick={() => handleTabChange("Calendar")}
                  className={`w-full flex items-center px-4 py-3 rounded-xl font-medium transition-colors ${activeTab === "Calendar"
                    ? "bg-blue-600 text-white shadow"
                    : "hover:bg-gray-800 text-gray-300"
                    }`}
                >
                  <Calendar className="w-5 h-5 mr-3" /> Calendar
                </button>
                <button
                  onClick={() => handleTabChange("uploadcontent")}
                  className={`w-full flex items-center px-4 py-3 rounded-xl font-medium transition-colors ${activeTab === "uploadcontent"
                    ? "bg-blue-600 text-white shadow"
                    : "hover:bg-gray-800 text-gray-300"
                    }`}
                >
                  <Upload className="w-5 h-5 mr-3" />
                  Upload Content
                </button>
                <button
                  onClick={() => {
                    setActiveTab(""); // Clear active tab when navigating to questionnaires
                    navigate("/model/questionnaires");
                  }}
                  className={`w-full flex items-center px-4 py-3 rounded-xl font-medium transition-colors ${isQuestionnairesRoute
                    ? "bg-blue-600 text-white shadow"
                    : "hover:bg-gray-800 text-gray-300"
                    }`}
                >
                  <FileText className="w-5 h-5 mr-3" /> Questionnaires
                </button>
                <button
                  onClick={() => handleTabChange("MyProfile")}
                  className={`w-full flex items-center px-4 py-3 rounded-xl font-medium transition-colors ${activeTab === "MyProfile"
                    ? "bg-blue-600 text-white shadow"
                    : "hover:bg-gray-800 text-gray-300"
                    }`}
                >
                  <CircleUser className="w-5 h-5 mr-3" /> MyProfile
                </button>
                <button
                  onClick={() => handleTabChange("My Support Team")}
                  className={`w-full flex items-center px-4 py-3 rounded-xl font-medium transition-colors ${activeTab === "My Support Team"
                    ? "bg-blue-600 text-white shadow"
                    : "hover:bg-gray-800 text-gray-300"
                    }`}
                >
                  <Settings className="w-5 h-5 mr-3" /> My Support Team
                </button>
                <button
                  onClick={() => handleTabChange("Settings")}
                  className={`w-full flex items-center px-4 py-3 rounded-xl font-medium transition-colors ${activeTab === "Settings"
                    ? "bg-blue-600 text-white shadow"
                    : "hover:bg-gray-800 text-gray-300"
                    }`}
                >
                  <Settings className="w-5 h-5 mr-3" /> Settings
                </button>
              </nav>
            </div>
            <button
              onClick={logout}
              className="flex items-center justify-center w-full px-4 py-3 mt-8 bg-red-600 hover:bg-red-700 rounded-xl font-semibold shadow"
            >
              <LogOut className="w-5 h-5 mr-2" /> Logout
            </button>
          </aside>

          {/* Main Content Area */}
          <main className="flex-1 p-10 h-screen overflow-y-auto">
            {isQuestionnairesRoute ? (
              <AssignedQuestionnaires
                key={location.pathname + location.search} // Force remount when URL changes
                modelId={user._id}
                onStartQuestionnaire={(assignment) => {
                  // Navigate to questionnaire form
                  navigate(`/model/questionnaire/${assignment._id}`);
                }}
              />
            ) : activeTab === "Messenger" ? (
              <ModelMessanger />
            ) : activeTab === "Tasks" ? (
              <BoardsView modelId={user._id} />
            ) : activeTab === "Traffic & Analytics" ? (
              <InstagramDashboard Id={user._id} role={user.role} />
            ) : activeTab === "Calendar" ? (
              <ModelCalender modelId={user._id} isModel={true} />
            ) : activeTab === "uploadcontent" ? (
              <div>
                <ContentUpload modelId={user._id} isModel={true} />
              </div>
            ) : activeTab === "MyProfile" ? (
              <MyProfile modelId={user._id} isModel={true} />
            ) : activeTab === "My Support Team" ? (
              <Supportteam />
            ) : activeTab === "Settings" ? (
              <Settings modelId={user._id} isModel={true} />
            ) : (
              <div className="text-gray-400 text-xl font-medium">
                Coming Soon: {activeTab}
              </div>
            )}
          </main>
        </div>
      </div>

      {/* Search Modal Overlay */}
      <Modal open={isSearchOpen} onClose={closeSearch}>
        <div className="flex justify-end p-4 pb-2">
          <Button variant="ghost" size="icon" onClick={closeSearch}>
            <X size={18} />
          </Button>
        </div>
        <div className="px-6 pb-6">
          <SearchInput
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search models and users..."
            autoFocus
          />
          <div className="mb-4 max-h-64 overflow-y-auto">
            {searchResults.length > 0 ? (
              <div className="space-y-1">
                {searchResults.map((result, index) => (
                  <div
                    key={index}
                    onClick={() => handleModelClick(result._id)}
                    className="flex items-center p-3 rounded-lg cursor-pointer transition-colors hover:bg-blue-600/30 group"
                  >
                    <Avatar
                      src={result.profilePhoto}
                      alt={result.fullName}
                      fallback={result.fullName?.[0] || "U"}
                      className="w-10 h-10 mr-3 border-2 border-blue-600 shadow"
                    />
                    <div className="flex-1">
                      <span className="text-white font-medium block">
                        {result.fullName}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : searchQuery ? (
              <div className="text-gray-400 text-center py-8">
                <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No results found for "{searchQuery}"</p>
              </div>
            ) : (
              <div className="text-gray-400 text-center py-8">
                <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>Start typing to search models and users</p>
              </div>
            )}
          </div>
        </div>
      </Modal>

      {/* Add Model Modal Overlay */}
      <Modal open={isAddModalOpen} onClose={closeModal}>
        <div className="flex justify-end p-4 pb-2">
          <Button variant="ghost" size="icon" onClick={closeModal}>
            <X size={18} />
          </Button>
        </div>
        <div className="px-6 pb-6">
          <SearchInput
            value={searchQuery}
            onChange={(e) => handleGlobalModelSearch(e.target.value)}
            placeholder="Search for models..."
          />
          <div className="mb-6 max-h-48 overflow-y-auto">
            {searchResults.map((user) => (
              <div
                key={user._id}
                onClick={() => setSelectedUser(user)}
                className={`flex items-center p-3 rounded-lg cursor-pointer transition-colors ${selectedUser?._id === user._id
                  ? "bg-blue-600/80"
                  : "hover:bg-blue-600/30"
                  }`}
              >
                <Avatar
                  src={user.profilePhoto}
                  alt={user.fullName}
                  fallback={user.fullName?.[0] || "U"}
                  className="w-10 h-10 mr-3 border-2 border-blue-600 shadow"
                />
                <span className="text-white font-medium">{user.fullName}</span>
              </div>
            ))}
            {searchResults.length === 0 && (
              <div className="text-gray-400 text-center py-4">
                No users found
              </div>
            )}
          </div>
          <Button
            onClick={handleAddModelToAgency}
            disabled={!selectedUser}
            className="w-full py-3 mt-2"
          >
            Add Model
          </Button>
        </div>
      </Modal>
    </>
  );
}
