// src/components/SocketEventsListener.jsx
import React, { useEffect } from "react";
import { useDispatch } from "react-redux";
import { addNewDmconversation, addNewDmMessage, setMessageSeen, setMessageSent, setTyping, updateUserStatus } from "../globalstate/dmSlice";

import socket from "./socket";

const SocketEventsListener = () => {
  const dispatch = useDispatch();
  const user = JSON.parse(localStorage.getItem('auth'))?.user

  useEffect(() => {
    // DM: receive new message
    socket.on("dm:new_message", (message) => {
      dispatch(addNewDmMessage(message));
    });


    socket.on("dm:message_sent", (message) => {
      dispatch(setMessageSent(message));
    });

    socket.on("dm:set_typing", (data) => {
      dispatch(setTyping(data));
    });
    socket.on("update_user_status", (statusList) => {
      dispatch(updateUserStatus(statusList));
  
    });
    socket.on("dm:add_incoming_dm", (dmconversation) => {
      dispatch(addNewDmconversation([dmconversation, user._id]));
    });
    socket.on("dm:message_seen", (data) => {
      dispatch(setMessageSeen({convoId: data.convoId, messageId: data.messageId}))
    });
   


    // Cleanup on unmount
    return () => {
      socket.off("dm:new_message");
      socket.off("dm:message_sent");
      socket.off("dm:set_typing");
      socket.off("dm:update_user_status");
      socket.off("dm:add_incoming_dm");
      socket.off("dm:message_seen");
    };
  }, [dispatch]);

  return null; // doesn't render anything
};

export default React.memo(SocketEventsListener);
